install:
  couchbaseOperator: true
  admissionController: false
  couchbaseCluster: true
  syncGateway: false

couchbaseOperator:
  name: "couchbase-operator"
  image:
    repository: VALUES_images_couchbaseOperator_repository
    tag: VALUES_images_couchbaseOperator_tag
  imagePullPolicy: Always
  resources:
    requests:
      cpu: 50m
      memory: 128Mi
    limits:
      cpu: 700m
      memory: 500Mi
  nodeSelector: SUBTREE_couchbaseOperator_nodeSelector.12
  tolerations: SUBTREE_couchbaseOperator_tolerations.12
  affinity: SUBTREE_couchbaseOperator_affinity.12

syncGateway:
  configSecret: " "

buckets:
  default: null

backups:
  default-backup:
    name: backup
    strategy: full_incremental
    full:
      schedule: VALUES_backups_full_schedule
    incremental:
      schedule: VALUES_backups_incremental_schedule
    size: VALUES_backups_size
    s3bucket: VALUES_backup_bucket
    storageClassName: cet-ebs
    failedJobsHistoryLimit: 1
    successfulJobsHistoryLimit: 1

cluster:
  antiAffinity: true
  enableOnlineVolumeExpansion: true
  envImagePrecedence: null
  autoResourceAllocation:
    enabled: false
    cpuRequests: "4"
    cpuLimits: "8"
    overheadPercent: 25
  cluster:
    autoFailoverTimeout: VALUES_cluster_cluster_autoFailoverTimeout
    dataServiceMemoryQuota: VALUES_cluster_cluster_dataServiceMemoryQuota
    indexServiceMemoryQuota: VALUES_cluster_cluster_indexServiceMemoryQuota
    queryServiceMemoryQuota: VALUES_cluster_cluster_queryServiceMemoryQuota
    autoFailoverMaxCount: VALUES_cluster_cluster_autoFailoverMaxCount
    data:
      minReplicasCount: VALUES_cluster_cluster_data_minReplicasCount
    autoCompaction:
      timeWindow:
        start: 00:00
        end: 23:59
    indexStorageSetting: plasma
    indexer:
      storageMode: plasma
      numReplica: 1
      redistributeIndexes: true
  image: VALUES_images_couchbaseServer_repository:VALUES_images_couchbaseServer_tag
  buckets:
    managed: false
  monitoring:
    prometheus:
      image: "couchbasesamples/sync-gateway-prometheus-exporter:latest"
      enabled: false
      resources:
        requests:
          cpu: 300m
          memory: 512Mi
        limits:
          cpu: 500m
          memory: 1Gi
  logging:
    audit:
      enabled: VALUES_cluster_logging_audit_enabled
      disabledEvents: JSON_cluster_logging_audit_disabledEvents
      rotation:
        interval: VALUES_cluster_logging_audit_rotation_interval
        pruneAge: VALUES_cluster_logging_audit_rotation_pruneAge
        size: VALUES_cluster_logging_audit_rotation_size
    server:
      enabled: VALUES_cluster_logging_server_enabled
      manageConfiguration: false
      configurationName: fluent-bit-config
      sidecar:
        image: "VALUES_images_fluentBit_repository:VALUES_images_fluentBit_tag"
        resources:
          requests:
            cpu: VALUES_cluster_logging_server_sidecar_resources_requests_cpu
            memory: VALUES_cluster_logging_server_sidecar_resources_requests_memory
          limits:
            cpu: VALUES_cluster_logging_server_sidecar_resources_limits_cpu
            memory: VALUES_cluster_logging_server_sidecar_resources_limits_memory
  name: couchbase-done
  networking:
    networkPlatform: Istio
    exposeAdminConsole: VALUES_cluster_networking_exposeAdminConsole
    disableUIOverHTTP: VALUES_cluster_networking_disableHttpUi
    disableUIOverHTTPS: VALUES_cluster_networking_disableHttpsUi
    adminConsoleServiceTemplate: null
    exposedFeatures: []
    exposedFeatureServiceTemplate: null
    dns:
      domain: VALUES_domain
    tls:
      static:
        serverSecret: null
      secretSource:
        serverSecretName: couchbase-cert
        clientSecretName: couchbase-client-cert
      nodeToNodeEncryption: VALUES_cluster_networking_tls_nodeToNodeEncryption
      clientCertificatePaths:
      - path: subject.cn
      clientCertificatePolicy: enable
  security:
    adminSecret: couchbase-admin-secret
    rbac:
      managed: false
    podSecurityContext:
      fsGroup: 1000
      runAsNonRoot: true
      runAsUser: 1000
      runAsGroup: 1000
      sysctls: []
    securityContext:
      allowPrivilegeEscalation: false
  backup:
    managed: true
    image: "VALUES_images_cbBackup_repository:VALUES_images_cbBackup_tag"
    serviceAccountName: backup-couchbase
    s3Secret: backup-secret
    resources:
      requests:
        cpu: VALUES_cluster_backup_resources_requests_cpu
        memory: VALUES_cluster_backup_resources_requests_memory
      limits:
        cpu: VALUES_cluster_backup_resources_limits_cpu
        memory: VALUES_cluster_backup_resources_limits_memory
    nodeSelector: SUBTREE_cluster_backup_nodeSelector.12
    tolerations: SUBTREE_cluster_backup_tolerations.12
  serverGroups:
  - eu-central-1a
  - eu-central-1b
  - eu-central-1c
  servers:
    default: null
    data:
      pod:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
          labels:
            app: couchbase
            app.kubernetes.io/name: couchbase
            version: VALUES_images_couchbaseServer_tag
        spec:
          nodeSelector: SUBTREE_cluster_servers_data_nodeSelector.12
          tolerations: SUBTREE_cluster_servers_data_tolerations.12
          affinity: SUBTREE_cluster_servers_data_affinity.12
      services:
      - data
      size: VALUES_cluster_servers_data_size
      resources: SUBTREE_cluster_servers_data_resources.12
      volumeMounts:
        default: couchbase-data
    index:
      pod:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
          labels:
            app: couchbase
            app.kubernetes.io/name: couchbase
            version: VALUES_images_couchbaseServer_tag
        spec:
          nodeSelector: SUBTREE_cluster_servers_index_nodeSelector.12
          tolerations: SUBTREE_cluster_servers_index_tolerations.12
          affinity: SUBTREE_cluster_servers_index_affinity.12
      services:
      - index
      size: VALUES_cluster_servers_index_size
      resources: SUBTREE_cluster_servers_index_resources.12
      volumeMounts:
        default: couchbase-index
    query:
      pod:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
          labels:
            app: couchbase
            app.kubernetes.io/name: couchbase
            version: VALUES_images_couchbaseServer_tag
        spec:
          nodeSelector: SUBTREE_cluster_servers_query_nodeSelector.12
          tolerations: SUBTREE_cluster_servers_query_tolerations.12
          affinity: SUBTREE_cluster_servers_query_affinity.12
      services:
      - query
      size: VALUES_cluster_servers_query_size
      resources: SUBTREE_cluster_servers_query_resources.12
      volumeMounts:
        default: couchbase-query
  volumeClaimTemplates:
  - metadata:
      name: couchbase-data
    spec:
      storageClassName: "cet-ebs"
      resources:
        requests:
          storage: VALUES_cluster_servers_data_storage_size
  - metadata:
      name: couchbase-index
    spec:
      storageClassName: "cet-ebs"
      resources:
        requests:
          storage: VALUES_cluster_servers_index_storage_size
  - metadata:
      name: couchbase-query
    spec:
      storageClassName: "cet-ebs"
      resources:
        requests:
          storage: VALUES_cluster_servers_query_storage_size
