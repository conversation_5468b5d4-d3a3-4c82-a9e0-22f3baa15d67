#!/usr/bin/env bash

set -e -o pipefail

chart_version="2.80.0"
repo_name="couchbase-partners"
chart_name="couchbase-operator"
repo_url="https://couchbase-partners.github.io/helm-charts"
release_name="couchbase-operator"
release_name_short="couchbase-done"

pushd "$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)" 1>/dev/null || exit

helm_ensure_repo.sh $repo_name $repo_url

# values-default.yml
tmp_dir=$(mktemp -d -t helm_render-XXXXXXXXXX)
pushd "$tmp_dir" >/dev/null || exit
helm pull $repo_name/$chart_name --version $chart_version
popd >/dev/null || exit
helm show values "$tmp_dir/$chart_name-$chart_version.tgz" >values-default.yml
# ensure file is terminated with a newline (see posix 3.206 Line)
sed -i -e :a -e '/^\n*$/{$d;N;};/\n$/ba' values-default.yml
rm -rf "$tmp_dir"

generated_path="../../chart/templates/$release_name_short/generated"

rm -rf "$generated_path"
mkdir -p "$generated_path"

helm template $release_name $repo_name/$chart_name \
    --namespace couchbase-done \
    --values ./values.yml \
    --version "$chart_version" \
    --no-hooks \
    --output-dir $generated_path

echo "Fix TLS"
yq d -i $generated_path/couchbase-operator/templates/couchbase-cluster.yaml spec.networking.tls.static

echo "Fix Service Account"
yq m -i $generated_path/$chart_name/templates/couchbase-backup.yaml irsa_patch.yaml

echo "Add Operator Karpenter Annotation"
yq m -i -d4 $generated_path/$chart_name/templates/operator-deployment.yaml operator_patch.yaml

echo "Fix Security Context"
yq m -i -d4 $generated_path/$chart_name/templates/operator-deployment.yaml security_patch.yaml

echo "Fix Istio Proxy Sidecar Limits"
yq m -i -d4 $generated_path/$chart_name/templates/operator-deployment.yaml istio_patch.yaml

echo "Remove s3bucket option from CouchbaseBackup"
sed -i '/s3bucket/d' $generated_path/$chart_name/templates/couchbase-backup.yaml

echo "Remove migration block"
yq d -i $generated_path/couchbase-operator/templates/couchbase-cluster.yaml spec.migration

echo "Add thread size to couchbase backup"
yq m -i -d3 $generated_path/$chart_name/templates/couchbase-backup.yaml backup_patch.yaml

echo "Remove cloudNativeGateway from CouchbaseCluster"
yq d -i $generated_path/couchbase-operator/templates/couchbase-cluster.yaml spec.networking.cloudNativeGateway

echo "fix templating"
helm_fix_templating.sh $generated_path

echo "fix templating fruity"
helm_fix_templating_fruity.sh $generated_path
