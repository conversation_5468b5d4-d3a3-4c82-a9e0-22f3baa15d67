---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: ssm-couchbase-secret-store
  namespace: {{ .Release.Namespace }}
spec:
  provider:
    aws:
      service: ParameterStore
      region: eu-central-1
      auth:
        jwt:
          serviceAccountRef:
            name: "couchbase-done-external-secrets"
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: couchbase-admin-secret
  namespace: {{ .Release.Namespace }}
spec:
  refreshInterval: 5m0s
  secretStoreRef:
    name: ssm-couchbase-secret-store
  target:
    name: couchbase-admin-secret
    creationPolicy: Owner
  data:
    - secretKey: username
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/admin
        property: username
    - secretKey: password
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/admin
        property: password

{{- with .Values.users }}
{{- range $user, $value := . }}
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ $value.secretName }}
  namespace: {{ $.Release.Namespace }}
spec:
  refreshInterval: 5m0s
  secretStoreRef:
    name: ssm-couchbase-secret-store
  target:
    name: {{ $value.secretName }}
    creationPolicy: Owner
  data:
    - secretKey: username
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/{{ $user }}
        property: username
    - secretKey: password
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/{{ $user }}
        property: password
{{- end }}
{{- end }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: couchbase-done-external-secrets
  annotations:
    eks.amazonaws.com/role-arn: {{ .Values.externalSecrets.roleArn }}
