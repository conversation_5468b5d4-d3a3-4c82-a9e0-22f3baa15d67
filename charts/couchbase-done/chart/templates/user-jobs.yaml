
{{- range $user, $users := .Values.users }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: couchbase-{{ $user }}-user-create
  labels:
    {{- include "couchbase-done.labels" $ | nindent 4 }}
    app.kubernetes.io/component: user-management
spec:
  backoffLimit: {{ $.Values.jobs.userCreation.backoffLimit }}
  activeDeadlineSeconds: {{ $.Values.jobs.userCreation.activeDeadlineSeconds }}
  template:
    metadata:
      labels:
        {{- include "couchbase-done.selectorLabels" $ | nindent 8 }}
        app.kubernetes.io/component: user-management
    spec:
      containers:
      - command:
        - sh
        - /opt/scripts/{{ $user }}_create_user.sh
        image: {{ $.Values.images.couchbaseServer.repository }}:{{ $.Values.images.couchbaseServer.tag }}
        name: couchbase
        env:
        - name: ADMIN_USERNAME
          valueFrom:
            secretKeyRef:
              key: username
              name: couchbase-admin-secret
        - name: ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: couchbase-admin-secret
        - name: USERNAME
          valueFrom:
            secretKeyRef:
              key: username
              name: {{ $users.secretName }}
        - name: PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: {{ $users.secretName }}
        volumeMounts:
        - name: create-user-script
          mountPath: /opt/scripts
        - name: common-scripts
          mountPath: /scripts
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
              - ALL
      restartPolicy: OnFailure
      volumes:
      - name: create-user-script
        configMap:
          name: couchbase-create-user-scripts
          defaultMode: 0755
      - name: common-scripts
        configMap:
          name: couchbase-scripts
          defaultMode: 0755
      tolerations:
        {{- include "couchbase-done.tolerations" $ | nindent 8 }}
      affinity:
        {{- include "couchbase-done.affinity" $ | nindent 8 }}
      nodeSelector:
        {{- include "couchbase-done.nodeSelector" $ | nindent 8 }}
{{- end }}
