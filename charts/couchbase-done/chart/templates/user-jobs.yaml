
{{- range $user, $users := .Values.users }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: couchbase-{{ $user }}-user-create
spec:
  template:
    spec:
      containers:
      - command:
        - sh
        - /opt/scripts/{{ $user }}_create_user.sh
        image: {{ $.Values.images.couchbaseServer.repository }}:{{ $.Values.images.couchbaseServer.tag }}
        name: couchbase
        env:
        - name: ADMIN_USERNAME
          valueFrom:
            secretKeyRef:
              key: username
              name: couchbase-admin-secret
        - name: ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: couchbase-admin-secret
        - name: USERNAME
          valueFrom:
            secretKeyRef:
              key: username
              name: {{ $users.secretName }}
        - name: PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: {{ $users.secretName }}
        volumeMounts:
        - name: create-user-script
          mountPath: /opt/scripts
      restartPolicy: OnFailure
      volumes:
      - name: create-user-script
        configMap:
          name: couchbase-create-user-scripts
      tolerations:
      - key: "node.vodafone.com/provisioner"
        operator: "Equal"
        value: "cet-bottlerocket-couchbase"
        effect: "NoSchedule"
      - key: node.vodafone.com/arch
        operator: Equal
        value: arm64
      - key: node.vodafone.com/arch
        operator: Equal
        value: amd64
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node.vodafone.com/platform
                    operator: In
                    values:
                      - couchbase
                  - key: eks.amazonaws.com/compute-type
                    operator: NotIn
                    values:
                      - fargate
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - arm64
                      - amd64
                  - key: kubernetes.io/os
                    operator: In
                    values:
                      - linux
      nodeSelector: {}
{{- end }}
