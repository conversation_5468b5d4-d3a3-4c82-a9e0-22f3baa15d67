{{- with .Values.users }}
{{- range $user, $value := . }}
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ $value.secretName }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "couchbase-done.labels" $ | nindent 4 }}
    app.kubernetes.io/component: secret-management
    couchbase.com/user: {{ $user | quote }}
spec:
  refreshInterval: 5m0s
  secretStoreRef:
    name: ssm-couchbase-secret-store
  target:
    name: {{ $value.secretName }}
    creationPolicy: Owner
  data:
    - secretKey: username
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/{{ $user }}
        property: username
    - secretKey: password
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/{{ $user }}
        property: password
{{- end }}
{{- end }}
