apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: couchbase-client-cert
spec:
  commonName: admin
  issuerRef:
    group: awspca.cert-manager.io
    name: pca-issuer
    kind: AWSPCAClusterIssuer
  duration: 8760h0m0s # 260d
  renewBefore: 720h0m0s # 30d
  secretName: couchbase-client-cert
  usages:
    - client auth
  privateKey:
    algorithm: RSA
    encoding: PKCS8
    size: 2048
