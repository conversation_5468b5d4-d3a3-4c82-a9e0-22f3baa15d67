---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: couchbase-client-cert
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
spec:
  commonName: admin
  issuerRef:
    group: {{ include "couchbase-done.certIssuerGroup" . }}
    name: {{ include "couchbase-done.certIssuer" . }}
    kind: {{ include "couchbase-done.certIssuerKind" . }}
  duration: {{ .Values.certificates.duration }}
  renewBefore: {{ .Values.certificates.renewBefore }}
  secretName: couchbase-client-cert
  usages:
    - client auth
  privateKey:
    algorithm: RSA
    encoding: PKCS8
    size: 2048
