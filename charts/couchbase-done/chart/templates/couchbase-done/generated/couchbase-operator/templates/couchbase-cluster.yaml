# Source: couchbase-operator/templates/couchbase-cluster.yaml
apiVersion: "couchbase.com/v2"
kind: "CouchbaseCluster"
metadata:
  name: couchbase-done
spec:
  antiAffinity: true
  autoResourceAllocation:
    cpuLimits: "8"
    cpuRequests: "4"
    enabled: false
    overheadPercent: 25
  backup:
    image: {{ .Values.images.cbBackup.repository }}:{{ .Values.images.cbBackup.tag }}
    managed: true
    nodeSelector: {{- toYaml .Values.cluster.backup.nodeSelector | nindent 12 }}
    objectEndpoint:
      useVirtualPath: false
    resources:
      limits:
        cpu: {{ .Values.cluster.backup.resources.limits.cpu }}
        memory: {{ .Values.cluster.backup.resources.limits.memory }}
      requests:
        cpu: {{ .Values.cluster.backup.resources.requests.cpu }}
        memory: {{ .Values.cluster.backup.resources.requests.memory }}
    s3Secret: backup-secret
    serviceAccountName: backup-couchbase
    tolerations: {{- toYaml .Values.cluster.backup.tolerations | nindent 12 }}
    useIAMRole: false
  buckets:
    managed: false
    synchronize: false
  cluster:
    analyticsServiceMemoryQuota: 1Gi
    autoCompaction:
      databaseFragmentationThreshold:
        percent: 30
      parallelCompaction: false
      timeWindow:
        abortCompactionOutsideWindow: false
        end: "23:59"
        start: "00:00"
      tombstonePurgeInterval: 72h
      viewFragmentationThreshold:
        percent: 30
    autoFailoverMaxCount: {{ .Values.cluster.cluster.autoFailoverMaxCount }}
    autoFailoverOnDataDiskIssues: false
    autoFailoverOnDataDiskIssuesTimePeriod: 120s
    autoFailoverServerGroup: false
    autoFailoverTimeout: {{ .Values.cluster.cluster.autoFailoverTimeout }}
    data:
      minReplicasCount: {{ .Values.cluster.cluster.data.minReplicasCount }}
    dataServiceMemoryQuota: {{ .Values.cluster.cluster.dataServiceMemoryQuota }}
    eventingServiceMemoryQuota: 256Mi
    indexServiceMemoryQuota: {{ .Values.cluster.cluster.indexServiceMemoryQuota }}
    indexStorageSetting: plasma
    indexer:
      enablePageBloomFilter: false
      enableShardAffinity: false
      logLevel: info
      maxRollbackPoints: 2
      memorySnapshotInterval: 200ms
      numReplica: 1
      redistributeIndexes: true
      stableSnapshotInterval: 5s
      storageMode: plasma
    query:
      backfillEnabled: true
      cboEnabled: true
      cleanupClientAttemptsEnabled: true
      cleanupLostAttemptsEnabled: true
      cleanupWindow: 60s
      completedLimit: 4000
      completedMaxPlanSize: "262144"
      completedTrackingAllRequests: false
      completedTrackingEnabled: true
      completedTrackingThreshold: 7s
      logLevel: info
      maxParallelism: 1
      memoryQuota: "0"
      nodeQuotaValPercent: 67
      numActiveTransactionRecords: 1024
      numCpus: 0
      pipelineBatch: 16
      pipelineCap: 512
      preparedLimit: 16384
      scanCap: 512
      temporarySpace: 5Gi
      temporarySpaceUnlimited: false
      txTimeout: 0ms
      useReplica: false
    queryServiceMemoryQuota: {{ .Values.cluster.cluster.queryServiceMemoryQuota }}
    searchServiceMemoryQuota: 256Mi
  enableOnlineVolumeExpansion: true
  enablePreviewScaling: false
  hibernate: false
  image: {{ .Values.images.couchbaseServer.repository }}:{{ .Values.images.couchbaseServer.tag }}
  logging:
    audit:
      disabledEvents: {{ toJson .Values.cluster.logging.audit.disabledEvents }}
      enabled: {{ .Values.cluster.logging.audit.enabled }}
      garbageCollection:
        sidecar:
          age: 1h
          enabled: false
          image: busybox:1.33.1
          interval: 20m
      rotation:
        interval: {{ .Values.cluster.logging.audit.rotation.interval }}
        pruneAge: {{ .Values.cluster.logging.audit.rotation.pruneAge }}
        size: {{ .Values.cluster.logging.audit.rotation.size }}
    server:
      configurationName: fluent-bit-config
      enabled: {{ .Values.cluster.logging.server.enabled }}
      manageConfiguration: false
      sidecar:
        configurationMountPath: /fluent-bit/config/
        image: {{ .Values.images.fluentBit.repository }}:{{ .Values.images.fluentBit.tag }}
        resources:
          limits:
            cpu: {{ .Values.cluster.logging.server.sidecar.resources.limits.cpu }}
            memory: {{ .Values.cluster.logging.server.sidecar.resources.limits.memory }}
          requests:
            cpu: {{ .Values.cluster.logging.server.sidecar.resources.requests.cpu }}
            memory: {{ .Values.cluster.logging.server.sidecar.resources.requests.memory }}
  monitoring:
    prometheus:
      enabled: false
      image: couchbasesamples/sync-gateway-prometheus-exporter:latest
      resources:
        limits:
          cpu: 500m
          memory: 1Gi
        requests:
          cpu: 300m
          memory: 512Mi
  networking:
    adminConsoleServices:
      - data
    disableUIOverHTTP: {{ .Values.cluster.networking.disableHttpUi }}
    disableUIOverHTTPS: {{ .Values.cluster.networking.disableHttpsUi }}
    dns:
      domain: {{ .Values.domain }}
    exposeAdminConsole: {{ .Values.cluster.networking.exposeAdminConsole }}
    exposedFeatures: []
    networkPlatform: Istio
    tls:
      clientCertificatePaths:
        - path: subject.cn
      clientCertificatePolicy: enable
      nodeToNodeEncryption: {{ .Values.cluster.networking.tls.nodeToNodeEncryption }}
      secretSource:
        clientSecretName: couchbase-client-cert
        serverSecretName: couchbase-cert
    waitForAddressReachable: 10m
    waitForAddressReachableDelay: 2m
  paused: false
  perServiceClassPDB: false
  security:
    adminSecret: couchbase-admin-secret
    podSecurityContext:
      fsGroup: 1000
      runAsGroup: 1000
      runAsNonRoot: true
      runAsUser: 1000
      sysctls: []
      windowsOptions:
        hostProcess: false
    rbac:
      managed: false
    securityContext:
      allowPrivilegeEscalation: false
    uiSessionTimeout: 0
  serverGroups:
    - eu-central-1a
    - eu-central-1b
    - eu-central-1c
  servers:
    - name: data
      pod:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
          labels:
            app: couchbase
            app.kubernetes.io/name: couchbase
            version: {{ .Values.images.couchbaseServer.tag }}
        spec:
          affinity: {{- toYaml .Values.cluster.servers.data.affinity | nindent 12 }}
          nodeSelector: {{- toYaml .Values.cluster.servers.data.nodeSelector | nindent 12 }}
          tolerations: {{- toYaml .Values.cluster.servers.data.tolerations | nindent 12 }}
      resources: {{- toYaml .Values.cluster.servers.data.resources | nindent 12 }}
      services:
        - data
      size: {{ .Values.cluster.servers.data.size }}
      volumeMounts:
        default: couchbase-data
    - name: index
      pod:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
          labels:
            app: couchbase
            app.kubernetes.io/name: couchbase
            version: {{ .Values.images.couchbaseServer.tag }}
        spec:
          affinity: {{- toYaml .Values.cluster.servers.index.affinity | nindent 12 }}
          nodeSelector: {{- toYaml .Values.cluster.servers.index.nodeSelector | nindent 12 }}
          tolerations: {{- toYaml .Values.cluster.servers.index.tolerations | nindent 12 }}
      resources: {{- toYaml .Values.cluster.servers.index.resources | nindent 12 }}
      services:
        - index
      size: {{ .Values.cluster.servers.index.size }}
      volumeMounts:
        default: couchbase-index
    - name: query
      pod:
        metadata:
          annotations:
            karpenter.sh/do-not-disrupt: "true"
          labels:
            app: couchbase
            app.kubernetes.io/name: couchbase
            version: {{ .Values.images.couchbaseServer.tag }}
        spec:
          affinity: {{- toYaml .Values.cluster.servers.query.affinity | nindent 12 }}
          nodeSelector: {{- toYaml .Values.cluster.servers.query.nodeSelector | nindent 12 }}
          tolerations: {{- toYaml .Values.cluster.servers.query.tolerations | nindent 12 }}
      resources: {{- toYaml .Values.cluster.servers.query.resources | nindent 12 }}
      services:
        - query
      size: {{ .Values.cluster.servers.query.size }}
      volumeMounts:
        default: couchbase-query
  softwareUpdateNotifications: false
  volumeClaimTemplates:
    - metadata:
        name: couchbase-data
      spec:
        resources:
          requests:
            storage: {{ .Values.cluster.servers.data.storage.size }}
        storageClassName: cet-ebs
    - metadata:
        name: couchbase-index
      spec:
        resources:
          requests:
            storage: {{ .Values.cluster.servers.index.storage.size }}
        storageClassName: cet-ebs
    - metadata:
        name: couchbase-query
      spec:
        resources:
          requests:
            storage: {{ .Values.cluster.servers.query.storage.size }}
        storageClassName: cet-ebs
  xdcr:
    managed: false
