# Source: couchbase-operator/templates/operator-deployment.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: couchbase-operator-couchbase-operator
  labels:
    app.kubernetes.io/name: couchbase-operator
    app.kubernetes.io/instance: couchbase-operator
    app.kubernetes.io/managed-by: <PERSON><PERSON>
    helm.sh/chart: couchbase-operator-2.80.0
---
# Source: couchbase-operator/templates/operator-deployment.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: couchbase-operator-couchbase-operator
  labels:
    app.kubernetes.io/name: couchbase-operator
    app.kubernetes.io/instance: couchbase-operator
    app.kubernetes.io/managed-by: Helm
    helm.sh/chart: couchbase-operator-2.80.0
rules:
  - apiGroups:
      - batch
    resources:
      - jobs
      - cronjobs
    verbs:
      - list
      - watch
      - create
      - update
      - delete
  - apiGroups:
      - couchbase.com
    resources:
      - couchbaseclusters
      - couchbaseclusters/finalizers
    verbs:
      - get
      - list
      - watch
      - update
  - apiGroups:
      - couchbase.com
    resources:
      - couchbasereplications
      - couchbasemigrationreplications
      - couchbaseusers
      - couchbasegroups
      - couchbaserolebindings
      - couchbasebackups
    verbs:
      - list
      - watch
  - apiGroups:
      - couchbase.com
    resources:
      - couchbasebuckets
      - couchbaseephemeralbuckets
      - couchbasememcachedbuckets
      - couchbasescopes
      - couchbasescopegroups
      - couchbasecollections
      - couchbasecollectiongroups
    verbs:
      - list
      - watch
      - create
  - apiGroups:
      - couchbase.com
    resources:
      - couchbasebackuprestores
    verbs:
      - list
      - watch
      - delete
  - apiGroups:
      - couchbase.com
    resources:
      - couchbaseautoscalers
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - delete
  - apiGroups:
      - couchbase.com
    resources:
      - couchbaseautoscalers/status
    verbs:
      - update
  - apiGroups:
      - ""
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - delete
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/status
      - services
      - persistentvolumeclaims
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - delete
      - patch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - list
      - create
      - update
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - get
      - create
      - update
      - list
      - watch
  - apiGroups:
      - policy
    resources:
      - poddisruptionbudgets
    verbs:
      - get
      - create
      - delete
      - list
      - watch
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - get
      - create
      - update
---
# Source: couchbase-operator/templates/operator-deployment.yaml
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: couchbase-operator-couchbase-operator
  labels:
    app.kubernetes.io/name: couchbase-operator
    app.kubernetes.io/instance: couchbase-operator
    app.kubernetes.io/managed-by: Helm
    helm.sh/chart: couchbase-operator-2.80.0
subjects:
  - kind: ServiceAccount
    name: couchbase-operator-couchbase-operator
    namespace: couchbase-done
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: couchbase-operator-couchbase-operator
---
# Source: couchbase-operator/templates/operator-deployment.yaml
apiVersion: v1
kind: Service
metadata:
  name: couchbase-operator-couchbase-operator
  labels:
    app.kubernetes.io/name: couchbase-operator
    app.kubernetes.io/instance: couchbase-operator
    app.kubernetes.io/managed-by: Helm
    helm.sh/chart: couchbase-operator-2.80.0
spec:
  ports:
    - name: http-pprof
      port: 8080
      protocol: TCP
      targetPort: 8080
    - name: http-prometheus
      port: 8383
      protocol: TCP
      targetPort: 8383
  selector:
    app.kubernetes.io/name: couchbase-operator
---
# Source: couchbase-operator/templates/operator-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: couchbase-operator-couchbase-operator
  labels:
    app.kubernetes.io/name: couchbase-operator
    app.kubernetes.io/instance: couchbase-operator
    app.kubernetes.io/managed-by: Helm
    helm.sh/chart: couchbase-operator-2.80.0
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: couchbase-operator
  template:
    metadata:
      labels:
        app.kubernetes.io/name: couchbase-operator
      annotations:
        karpenter.sh/do-not-disrupt: "true"
        sidecar.istio.io/proxyMemoryLimit: 512Mi
        sidecar.istio.io/proxyCPULimit: 300m
    spec:
      serviceAccountName: couchbase-operator-couchbase-operator
      imagePullSecrets:
      containers:
        - name: couchbase-operator
          image: "{{ .Values.images.couchbaseOperator.repository }}:{{ .Values.images.couchbaseOperator.tag }}"
          imagePullPolicy: Always
          command:
            - couchbase-operator
          args:
            - "--pod-create-timeout=10m"
          env:
            - name: WATCH_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          ports:
            - containerPort: 8080
              name: http
            - containerPort: 8383
              name: prometheus
          readinessProbe:
            httpGet:
              path: /readyz
              port: http
              scheme: HTTP
          resources:
            limits:
              cpu: 700m
              memory: 500Mi
            requests:
              cpu: 50m
              memory: 128Mi
          securityContext:
            readOnlyRootFilesystem: true
      nodeSelector: {{- toYaml .Values.couchbaseOperator.nodeSelector | nindent 12 }}
      affinity: {{- toYaml .Values.couchbaseOperator.affinity | nindent 12 }}
      tolerations: {{- toYaml .Values.couchbaseOperator.tolerations | nindent 12 }}
      securityContext:
        runAsUser: 10000
        runAsGroup: 10000
        fsGroup: 10000
