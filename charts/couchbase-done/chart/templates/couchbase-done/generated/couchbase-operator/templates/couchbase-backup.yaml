# Source: couchbase-operator/templates/couchbase-backup.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: backup-couchbase
  annotations:
    eks.amazonaws.com/role-arn: {{ .Values.cluster.backup.roleArn }}
---
# Source: couchbase-operator/templates/couchbase-backup.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: backup-couchbase
rules:
  - apiGroups:
      - batch
    resources:
      - jobs
      - cronjobs
    verbs:
      - get
      - list
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
      - list
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
  - apiGroups:
      - couchbase.com
    resources:
      - couchbasebackups
      - couchbasebackuprestores
    verbs:
      - get
      - list
      - watch
      - patch
      - update
---
# Source: couchbase-operator/templates/couchbase-backup.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: backup-couchbase
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: backup-couchbase
subjects:
  - kind: ServiceAccount
    name: backup-couchbase
    namespace: couchbase-done
---
# Source: couchbase-operator/templates/couchbase-backup.yaml
apiVersion: v1
kind: List
items:
  - apiVersion: couchbase.com/v2
    kind: CouchbaseBackup
    metadata:
      name: backup
      labels:
        cluster: couchbase-done
    spec:
      failedJobsHistoryLimit: 1
      full:
        schedule: {{ .Values.backups.full.schedule }}
      incremental:
        schedule: {{ .Values.backups.incremental.schedule }}
      size: {{ .Values.backups.size }}
      storageClassName: cet-ebs
      strategy: full_incremental
      successfulJobsHistoryLimit: 1
      threads: {{ .Values.backups.threads }}
      ephemeralVolume: false
