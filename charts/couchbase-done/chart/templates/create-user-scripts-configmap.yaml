---
apiVersion: v1
kind: ConfigMap
metadata:
  name: couchbase-create-user-scripts
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
data:
{{- template "create_user_script" $ }}

{{- define "create_user_script" }}
  {{- range $user, $config := .Values.users }}
  {{ $user }}_create_user.sh: |-
    # Wait for Couchbase cluster to be ready
    timeout {{ $.Values.timeouts.couchbaseCli.userManagement }} bash -c 'until curl -k {{ include "couchbase-done.clusterServiceUrlHttps" $ }} -u $ADMIN_USERNAME:$ADMIN_PASSWORD; do sleep 10; done'

    # Create or update user with retry logic
    for attempt in {1..3}; do
      echo "Attempt $attempt: Creating/updating user {{ $user }}"
      if couchbase-cli user-manage \
        --cluster {{ include "couchbase-done.clusterServiceUrlSecure" $ }} \
        --username $ADMIN_USERNAME \
        --password=$ADMIN_PASSWORD \
        --auth-domain local \
        --set \
        --rbac-username=$USERNAME \
        --rbac-password=$PASSWORD \
        --roles {{ $config.roles }} \
        --no-ssl-verify; then
        echo "Successfully created/updated user {{ $user }}"
        exit 0
      else
        echo "Failed to create/update user {{ $user }}, attempt $attempt"
        if [ $attempt -lt 3 ]; then
          sleep 10
        fi
      fi
    done
    echo "Failed to create/update user {{ $user }} after 3 attempts"
    exit 1
  {{- end }}
{{- end -}}
