apiVersion: v1
kind: ConfigMap
metadata:
  name: couchbase-create-user-scripts
data:
{{- template "create_user_script" $ }}

{{- define "create_user_script" }}
  {{- range $user, $config := .Values.users }}
  {{ $user }}_create_user.sh: |-
    timeout 5m bash -c 'until curl -k https://{{ $.Values.cluster.name }}-srv.{{ $.Release.Namespace }}.svc.cluster.local:18091 -u $ADMIN_USERNAME:$ADMIN_PASSWORD; do sleep 10; done'
    couchbase-cli user-manage \
      --cluster couchbases://{{ $.Values.cluster.name }}-srv.{{ $.Release.Namespace }}.svc.cluster.local \
      --username $ADMIN_USERNAME \
      --password=$ADMIN_PASSWORD \
      --auth-domain local \
      --set \
      --rbac-username=$USERNAME \
      --rbac-password=$PASSWORD \
      --roles {{ $config.roles }} \
      --no-ssl-verify
  {{- end }}
{{- end -}}
