---
apiVersion: v1
kind: ConfigMap
metadata:
  name: couchbase-create-user-scripts
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
data:
{{- template "create_user_script" $ }}

{{- define "create_user_script" }}
  {{- range $user, $config := .Values.users }}
  {{ $user }}_create_user.sh: |-
    #!/bin/bash
    set -euo pipefail

    # Source common functions
    source /scripts/common-functions.sh

    # Validate required environment variables
    validate_env "ADMIN_USERNAME" "ADMIN_PASSWORD" "USERNAME" "PASSWORD" || exit 1

    echo "Creating/updating Couchbase user: {{ $user }}"
    echo "Description: {{ $config.description | default "No description provided" }}"
    echo "Roles: {{ $config.roles }}"

    # Wait for Couchbase cluster to be ready
    wait_for_couchbase "{{ $.Values.timeouts.couchbaseCli.userManagement }}" || exit 1

    # Create or update user with retry logic
    retry_with_backoff 3 5 "
        couchbase-cli user-manage \
          --cluster {{ include "couchbase-done.clusterServiceUrlSecure" $ }} \
          --username \$ADMIN_USERNAME \
          --password=\$ADMIN_PASSWORD \
          --auth-domain local \
          --set \
          --rbac-username=\$USERNAME \
          --rbac-password=\$PASSWORD \
          --roles '{{ $config.roles }}' \
          --no-ssl-verify
    " || exit 1

    echo "Successfully created/updated user {{ $user }}"
  {{- end }}
{{- end -}}
