---
apiVersion: v1
kind: Secret
metadata:
  name: fluent-bit-config
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: logging
stringData:
  fluent-bit.conf: |
    @include /fluent-bit/etc/couchbase/kubernetes-service.conf

    # Each of the logs should have a tag: couchbase.log.<logname>.
    @include /fluent-bit/etc/couchbase/input/in-tail-audit-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-xdcr-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-indexer-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-projector-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-memcached-log.conf

    # erlang multiline logs
    # @include /fluent-bit/etc/couchbase/input/in-tail-babysitter-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-couchdb-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-debug-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-json_rpc-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-mapreduce_errors-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-metakv-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-ns_couchdb-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-reports-log.conf

    # @include /fluent-bit/etc/couchbase/input/in-tail-analytics-log.conf

    # @include /fluent-bit/etc/couchbase/input/in-tail-eventing-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-fts-log.conf

    # @include /fluent-bit/etc/couchbase/input/in-tail-http-log.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-rebalance-report.conf
    # @include /fluent-bit/etc/couchbase/input/in-tail-prometheus-log.conf

    # Deal specifically with some log parsing initially
    @include /fluent-bit/etc/couchbase/filter/filter-handle-logfmt.conf

    # Add in common info
    @include /fluent-bit/etc/couchbase/filter/filter-add-common-info-kubernetes.conf

    # Deal with missing/incorrect level & filename information
    @include /fluent-bit/etc/couchbase/filter/filter-handle-levels.conf
    @include /fluent-bit/etc/couchbase/filter/filter-handle-filenames.conf

    # Output all parsed Couchbase logs by default
    @include /fluent-bit/etc/couchbase/output/out-stdout.conf
