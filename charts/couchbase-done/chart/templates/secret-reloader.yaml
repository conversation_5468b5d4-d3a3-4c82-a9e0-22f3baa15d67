apiVersion: apps/v1
kind: Deployment
metadata:
  name: couchbase-secret-reloader
spec:
  replicas: 1
  selector:
    matchLabels:
      app: couchbase-secret-reloader
  template:
    metadata:
      labels:
        app: couchbase-secret-reloader
    spec:
      terminationGracePeriodSeconds: 5
      containers:
      - name: couchbase-secret-reloader
        image: {{ .Values.images.secretReloader.repository }}:{{ .Values.images.secretReloader.tag }}
        command: ["/bin/bash", "-c"]
        args:
        - |
          # Install necessary tools
          # echo "Installing inotify-tools package"
          # apt-get update
          # apt-get install inotify-tools -y

          # Print the list of secrets being watched, excluding "admin"
          SECRETS_LIST=$(find /secrets/* -maxdepth 0 -type d -exec basename {} \; | grep -v "^admin$" | sed -e :a -e '$!N; s/\n/, /; ta')
          echo "Secrets to watch: ${SECRETS_LIST}"

          # Set up loop to monitor specified directories for changes
          echo "Setting up inotifywait loop to monitor mounted secrets under /secrets"
          inotifywait -e create -m -r /secrets |
          while read path action file; do
            if [[ "${file}" == "..data_tmp" && "${action}" == "CREATE" ]]; then
              # Ignore admin secret changes. Admin secret will be synced by the couchbase operator
              if [[ "${path}" =~ /secrets/admin/ ]]; then
                continue
              fi

              TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
              echo "[${TIMESTAMP}] Detected secret change under ${path}"
              if [[ "${path}" =~ /secrets/([^/]*)/ ]]; then
                secret_name="${BASH_REMATCH[1]}"

                # Get the admin username and password from the secret
                echo "Retrieving admin credentials from secret"
                ADMIN_USERNAME=$(cat /secrets/admin/username)
                ADMIN_PASSWORD=$(cat /secrets/admin/password)

                # Get the username and password from the secret
                echo "Retrieving ${secret_name} user credentials from secret"
                USERNAME=$(cat /secrets/${secret_name}/username)
                PASSWORD=$(cat /secrets/${secret_name}/password)

                # Get the script from the corresponding configmap
                echo "Retrieving script from ${configmap_name} configmap"
                script=$(cat /scripts/create-user-scripts/${secret_name}_create_user.sh)

                # Execute the script with the username and password as environment variables
                echo "Executing script for ${secret_name} secret"
                echo "$script" | env ADMIN_USERNAME="$ADMIN_USERNAME" ADMIN_PASSWORD="$ADMIN_PASSWORD" USERNAME="$USERNAME" PASSWORD="$PASSWORD" bash
              fi
            fi
          done
        volumeMounts:
        - name: secrets-admin
          mountPath: /secrets/admin
          readOnly: true
        {{- range $user, $values := .Values.users }}
        - name: secrets-{{ $user }}
          mountPath: /secrets/{{ $user }}
          readOnly: true
        {{- end }}
        - name: create-user-scripts
          mountPath: /scripts/create-user-scripts
          readOnly: true
        securityContext:
          readOnlyRootFilesystem: true
          privileged: false
          allowPrivilegeEscalation: false
      volumes:
      - name: secrets-admin
        secret:
          secretName: couchbase-admin-secret
      {{- range $user, $values := .Values.users }}
      - name: secrets-{{ $user }}
        secret:
          secretName: couchbase-{{ $user }}-secret
      {{- end }}
      - name: create-user-scripts
        configMap:
          name: couchbase-create-user-scripts
      nodeSelector: {{- toYaml .Values.secretReloader.nodeSelector | nindent 12 }}
      affinity: {{- toYaml .Values.secretReloader.affinity | nindent 12 }}
      tolerations: {{- toYaml .Values.secretReloader.tolerations | nindent 12 }}
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        runAsNonRoot: true
