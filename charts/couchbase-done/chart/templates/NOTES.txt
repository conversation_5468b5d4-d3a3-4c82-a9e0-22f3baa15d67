🚀 Couchbase Done has been deployed successfully!

📋 Deployment Summary:
  - Release Name: {{ .Release.Name }}
  - Namespace: {{ .Release.Namespace }}
  - Chart Version: {{ .Chart.Version }}
  - App Version: {{ .Chart.AppVersion }}
  - Cluster Name: {{ .Values.cluster.name }}

🔗 Access Information:
{{- if .Values.cluster.networking.exposeAdminConsole }}
  - Admin Console: https://{{ .Values.cluster.name }}.{{ .Values.domain }}
{{- end }}
  - Query Service: https://{{ .Values.cluster.name }}-query.{{ .Values.subdomain }}

📊 Cluster Configuration:
  - Data Nodes: {{ .Values.cluster.servers.data.size }}
  - Index Nodes: {{ .Values.cluster.servers.index.size }}
  - Query Nodes: {{ .Values.cluster.servers.query.size }}
  - Data Memory Quota: {{ .Values.cluster.cluster.dataServiceMemoryQuota }}
  - Index Memory Quota: {{ .Values.cluster.cluster.indexServiceMemoryQuota }}
  - Query Memory Quota: {{ .Values.cluster.cluster.queryServiceMemoryQuota }}

👥 Configured Users:
{{- range $user, $config := .Values.users }}
  - {{ $user }}: {{ $config.description | default "No description" }}
{{- end }}

💾 Backup Configuration:
  - Full Backup Schedule: {{ .Values.backups.full.schedule }}
  - Incremental Backup Schedule: {{ .Values.backups.incremental.schedule }}
  - Backup Storage Size: {{ .Values.backups.size }}
  - Snapshot Schedule: {{ .Values.snapshotSchedule.schedule }}

🔍 Useful Commands:

  # Check deployment status
  kubectl get pods -l app.kubernetes.io/instance={{ .Release.Name }}

  # View Couchbase cluster status
  kubectl get couchbaseclusters

  # Check certificates
  kubectl get certificates

  # View external secrets
  kubectl get externalsecrets

  # Check job status
  kubectl get jobs -l app.kubernetes.io/instance={{ .Release.Name }}

  # View logs
  kubectl logs -l app.kubernetes.io/name={{ include "couchbase-done.name" . }}

🔧 Troubleshooting:

  # Check operator logs
  kubectl logs -l app.kubernetes.io/name=couchbase-operator

  # Check admission controller
  kubectl logs -l app.kubernetes.io/name=couchbase-admission-controller

  # Verify network policies
  kubectl get networkpolicies

  # Check secret reloader
  kubectl logs deployment/couchbase-secret-reloader

📚 Documentation:
  For more information, see the README.md file in the chart directory.

⚠️  Important Notes:
  - Ensure all REPLACE_ME values in values.yaml are properly configured
  - Verify AWS IAM roles and permissions are correctly set up
  - Check that cert-manager is installed and configured
  - Confirm external-secrets operator is running

🎉 Happy Couchbasing!
