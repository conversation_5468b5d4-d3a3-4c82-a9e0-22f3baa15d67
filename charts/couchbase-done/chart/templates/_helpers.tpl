{{/*
Expand the name of the chart.
*/}}
{{- define "couchbase-done.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
*/}}
{{- define "couchbase-done.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "couchbase-done.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "couchbase-done.labels" -}}
helm.sh/chart: {{ include "couchbase-done.chart" . }}
{{ include "couchbase-done.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "couchbase-done.selectorLabels" -}}
app.kubernetes.io/name: {{ include "couchbase-done.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common tolerations for Couchbase workloads
*/}}
{{- define "couchbase-done.tolerations" -}}
- key: "node.vodafone.com/provisioner"
  operator: "Equal"
  value: "cet-bottlerocket-couchbase"
  effect: "NoSchedule"
- key: node.vodafone.com/arch
  operator: Equal
  value: arm64
- key: node.vodafone.com/arch
  operator: Equal
  value: amd64
{{- end }}

{{/*
Common node affinity for Couchbase workloads
*/}}
{{- define "couchbase-done.nodeAffinity" -}}
nodeAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
      - matchExpressions:
          - key: node.vodafone.com/platform
            operator: In
            values:
              - couchbase
          - key: eks.amazonaws.com/compute-type
            operator: NotIn
            values:
              - fargate
          - key: kubernetes.io/arch
            operator: In
            values:
              - arm64
              - amd64
          - key: kubernetes.io/os
            operator: In
            values:
              - linux
{{- end }}

{{/*
Common affinity configuration for Couchbase workloads
*/}}
{{- define "couchbase-done.affinity" -}}
{{- include "couchbase-done.nodeAffinity" . }}
{{- end }}

{{/*
Common nodeSelector for Couchbase workloads (empty by default, can be overridden)
*/}}
{{- define "couchbase-done.nodeSelector" -}}
{{- end }}

{{/*
Couchbase cluster service URL
*/}}
{{- define "couchbase-done.clusterServiceUrl" -}}
{{- printf "%s-srv.%s.svc.cluster.local" .Values.cluster.name .Release.Namespace }}
{{- end }}

{{/*
Couchbase cluster service URL with protocol
*/}}
{{- define "couchbase-done.clusterServiceUrlSecure" -}}
{{- printf "couchbases://%s" (include "couchbase-done.clusterServiceUrl" .) }}
{{- end }}

{{/*
Couchbase cluster service URL with HTTPS
*/}}
{{- define "couchbase-done.clusterServiceUrlHttps" -}}
{{- printf "https://%s:18091" (include "couchbase-done.clusterServiceUrl" .) }}
{{- end }}

{{/*
AWS region configuration
*/}}
{{- define "couchbase-done.awsRegion" -}}
{{- .Values.aws.region | default "eu-central-1" }}
{{- end }}

{{/*
Certificate issuer configuration
*/}}
{{- define "couchbase-done.certIssuer" -}}
{{- .Values.certificates.issuer | default "pca-issuer" }}
{{- end }}

{{/*
Certificate issuer group
*/}}
{{- define "couchbase-done.certIssuerGroup" -}}
{{- .Values.certificates.issuerGroup | default "awspca.cert-manager.io" }}
{{- end }}

{{/*
Certificate issuer kind
*/}}
{{- define "couchbase-done.certIssuerKind" -}}
{{- .Values.certificates.issuerKind | default "AWSPCAClusterIssuer" }}
{{- end }}
