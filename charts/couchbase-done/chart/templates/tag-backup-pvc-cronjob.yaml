---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tag-cb-backup-pvc
spec:
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  schedule: {{ .Values.tagBackupPvcCron.schedule }}
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: kubectl-pod
            image: "{{ .Values.tagBackupPvcCron.image.repository }}:{{ .Values.tagBackupPvcCron.image.tag }}"
            imagePullPolicy: {{ .Values.tagBackupPvcCron.image.pullPolicy }}
            securityContext:
              allowPrivilegeEscalation: false
              capabilities:
                drop:
                  - ALL
              runAsNonRoot: true
              runAsUser: 1000
            volumeMounts:
            command: ["/bin/sh"]
            args:
            - -c
            - kubectl -n {{ .Release.Namespace }} label pvc backup pvcpurpose=backup --overwrite
            imagePullPolicy: IfNotPresent
          restartPolicy: OnFailure
          serviceAccountName: tag-pvc-cj-sa
          tolerations:
          - key: "node.vodafone.com/provisioner"
            operator: "Equal"
            value: "cet-bottlerocket-couchbase"
            effect: "NoSchedule"
          - key: node.vodafone.com/arch
            operator: Equal
            value: arm64
          - key: node.vodafone.com/arch
            operator: Equal
            value: amd64
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: node.vodafone.com/platform
                        operator: In
                        values:
                          - couchbase
                      - key: eks.amazonaws.com/compute-type
                        operator: NotIn
                        values:
                          - fargate
                      - key: kubernetes.io/arch
                        operator: In
                        values:
                          - arm64
                          - amd64
                      - key: kubernetes.io/os
                        operator: In
                        values:
                          - linux
          nodeSelector: {}
      backoffLimit: 1
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tag-pvc-cj-sa
  namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tag-pvc-cj-role
  namespace: {{ .Release.Namespace }}
rules:
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tag-pvc-cj-rb
  namespace: {{ .Release.Namespace }}
subjects:
  - kind: ServiceAccount
    name: tag-pvc-cj-sa
roleRef:
  kind: Role
  name: tag-pvc-cj-role
  apiGroup: rbac.authorization.k8s.io
