---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tag-cb-backup-pvc
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: backup-management
spec:
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  schedule: {{ .Values.tagBackupPvcCron.schedule }}
  jobTemplate:
    metadata:
      labels:
        {{- include "couchbase-done.labels" . | nindent 8 }}
        app.kubernetes.io/component: backup-management
    spec:
      template:
        metadata:
          labels:
            {{- include "couchbase-done.selectorLabels" . | nindent 12 }}
            app.kubernetes.io/component: backup-management
        spec:
          containers:
          - name: kubectl-pod
            image: "{{ .Values.tagBackupPvcCron.image.repository }}:{{ .Values.tagBackupPvcCron.image.tag }}"
            imagePullPolicy: {{ .Values.tagBackupPvcCron.image.pullPolicy }}
            securityContext:
              allowPrivilegeEscalation: false
              capabilities:
                drop:
                  - ALL
              runAsNonRoot: true
              runAsUser: 1000
              readOnlyRootFilesystem: true
            command: ["/bin/sh"]
            args:
            - -c
            - kubectl -n {{ .Release.Namespace }} label pvc backup pvcpurpose=backup --overwrite
          restartPolicy: OnFailure
          serviceAccountName: tag-pvc-cj-sa
          tolerations:
            {{- include "couchbase-done.tolerations" . | nindent 12 }}
          affinity:
            {{- include "couchbase-done.affinity" . | nindent 12 }}
          nodeSelector:
            {{- include "couchbase-done.nodeSelector" . | nindent 12 }}
      backoffLimit: 1
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tag-pvc-cj-sa
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: backup-management
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tag-pvc-cj-role
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: backup-management
rules:
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tag-pvc-cj-rb
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: backup-management
subjects:
  - kind: ServiceAccount
    name: tag-pvc-cj-sa
roleRef:
  kind: Role
  name: tag-pvc-cj-role
  apiGroup: rbac.authorization.k8s.io
