---
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: ssm-couchbase-secret-store
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: secret-management
spec:
  provider:
    aws:
      service: ParameterStore
      region: {{ include "couchbase-done.awsRegion" . }}
      auth:
        jwt:
          serviceAccountRef:
            name: "couchbase-done-external-secrets"
