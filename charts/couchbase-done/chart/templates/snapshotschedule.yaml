---
apiVersion: snapscheduler.backube/v1
kind: SnapshotSchedule
metadata:
  name: daily
  namespace: {{ .Release.Namespace }}
spec:
  claimSelector:
    matchLabels:
      pvcpurpose: "backup" # use this claim selector to only snapshot backup pvc and not other pvc in this namespace
  disabled: false
  retention:
    expires: {{ .Values.snapshotSchedule.retentionExpiry }}
    maxCount: {{ .Values.snapshotSchedule.retentionMaxCount }}
  schedule: {{ .Values.snapshotSchedule.schedule }}
  snapshotTemplate:
    labels:
      schedule: daily
    snapshotClassName: cet-ebs-platform
