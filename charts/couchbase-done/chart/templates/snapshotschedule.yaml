---
apiVersion: snapscheduler.backube/v1
kind: SnapshotSchedule
metadata:
  name: daily
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: backup-management
spec:
  claimSelector:
    matchLabels:
      pvcpurpose: "backup" # use this claim selector to only snapshot backup pvc and not other pvc in this namespace
  disabled: false
  retention:
    expires: {{ .Values.snapshotSchedule.retentionExpiry }}
    maxCount: {{ .Values.snapshotSchedule.retentionMaxCount }}
  schedule: {{ .Values.snapshotSchedule.schedule }}
  snapshotTemplate:
    labels:
      schedule: daily
      {{- include "couchbase-done.labels" . | nindent 6 }}
    snapshotClassName: cet-ebs-platform
