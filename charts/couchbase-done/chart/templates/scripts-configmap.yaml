---
apiVersion: v1
kind: ConfigMap
metadata:
  name: couchbase-scripts
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: scripts
data:
  # Common functions for Couchbase operations
  common-functions.sh: |
    #!/bin/bash
    
    # Function to wait for Couchbase cluster to be ready
    wait_for_couchbase() {
        local timeout=${1:-{{ .Values.timeouts.couchbaseCli.defaultTimeout }}}
        local cluster_url="{{ include "couchbase-done.clusterServiceUrlHttps" . }}"
        
        echo "Waiting for Couchbase cluster to be ready at $cluster_url"
        timeout $timeout bash -c "
            until curl -k $cluster_url -u \$ADMIN_USERNAME:\$ADMIN_PASSWORD >/dev/null 2>&1; do
                echo 'Waiting for Couchbase cluster...'
                sleep 10
            done
        "
        
        if [ $? -eq 0 ]; then
            echo "Couchbase cluster is ready"
            return 0
        else
            echo "Timeout waiting for Couchbase cluster"
            return 1
        fi
    }
    
    # Function to retry a command with exponential backoff
    retry_with_backoff() {
        local max_attempts=$1
        local delay=$2
        local command="${@:3}"
        local attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            echo "Attempt $attempt/$max_attempts: $command"
            
            if eval "$command"; then
                echo "Command succeeded on attempt $attempt"
                return 0
            else
                echo "Command failed on attempt $attempt"
                
                if [ $attempt -lt $max_attempts ]; then
                    echo "Waiting ${delay}s before retry..."
                    sleep $delay
                    delay=$((delay * 2))  # Exponential backoff
                fi
            fi
            
            attempt=$((attempt + 1))
        done
        
        echo "Command failed after $max_attempts attempts"
        return 1
    }
    
    # Function to validate environment variables
    validate_env() {
        local required_vars=("$@")
        local missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if [ -z "${!var}" ]; then
                missing_vars+=("$var")
            fi
        done
        
        if [ ${#missing_vars[@]} -gt 0 ]; then
            echo "Error: Missing required environment variables: ${missing_vars[*]}"
            return 1
        fi
        
        return 0
    }
  
  # Health check script for containers
  health-check.sh: |
    #!/bin/bash
    
    # Basic health check for Couchbase services
    source /scripts/common-functions.sh
    
    # Check if required environment variables are set
    validate_env "ADMIN_USERNAME" "ADMIN_PASSWORD" || exit 1
    
    # Check cluster health
    cluster_url="{{ include "couchbase-done.clusterServiceUrlHttps" . }}"
    
    if curl -k -f "$cluster_url/pools/default" -u "$ADMIN_USERNAME:$ADMIN_PASSWORD" >/dev/null 2>&1; then
        echo "Cluster health check passed"
        exit 0
    else
        echo "Cluster health check failed"
        exit 1
    fi
