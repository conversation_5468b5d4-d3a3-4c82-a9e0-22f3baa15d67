---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: couchbase-cert
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
spec:
  commonName: couchbase-server
  dnsNames:
    # Core cluster DNS names
    - "*.{{ .Values.cluster.name }}"
    - "*.{{ .Values.cluster.name }}.{{ .Release.Namespace }}"
    - "*.{{ .Values.cluster.name }}.{{ .Release.Namespace }}.svc"
    - "*.{{ .Values.cluster.name }}.{{ .Release.Namespace }}.svc.cluster.local"
    - "{{ .Values.cluster.name }}-srv"
    - "{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}"
    - "{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc"
    - "{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc.cluster.local"
    - "*.{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc.cluster.local"
    # Standard names
    - localhost
    - cb-done-service
    - "{{ .Values.domain }}"
    - "*.{{ .Values.domain }}"
    # Legacy service names (maintained for backward compatibility)
    - "*.cb-1-service"
    - "cb-1-service-srv"
    - cb-1-service
    - "*.cb-2-service"
    - "cb-2-service-srv"
    - cb-2-service
    - "*.cb-3-service"
    - "cb-3-service-srv"
    - cb-3-service
    - "*.cb-4-service"
    - "cb-4-service-srv"
    - cb-4-service
    - "*.cb-5-service"
    - "cb-5-service-srv"
    - cb-5-service
    - "*.cb-6-service"
    - "cb-6-service-srv"
    - cb-6-service
    - "*.cb-7-service"
    - "cb-7-service-srv"
    - cb-7-service
    {{- range .Values.certificates.additionalDnsNames }}
    - {{ . | quote }}
    {{- end }}
  issuerRef:
    group: {{ include "couchbase-done.certIssuerGroup" . }}
    name: {{ include "couchbase-done.certIssuer" . }}
    kind: {{ include "couchbase-done.certIssuerKind" . }}
  duration: {{ .Values.certificates.duration }}
  renewBefore: {{ .Values.certificates.renewBefore }}
  secretName: couchbase-cert
  usages:
    - server auth
  privateKey:
    algorithm: RSA
    encoding: PKCS8
    size: 2048
