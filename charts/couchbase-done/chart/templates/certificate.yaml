---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: couchbase-cert
spec:
  commonName: couchbase-server
  dnsNames:
    - "*.{{ .Values.cluster.name }}"
    - "*.{{ .Values.cluster.name }}.{{ .Release.Namespace }}"
    - "*.{{ .Values.cluster.name }}.{{ .Release.Namespace }}.svc"
    - "*.{{ .Values.cluster.name }}.{{ .Release.Namespace }}.svc.cluster.local"
    - "{{ .Values.cluster.name }}-srv"
    - "{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}"
    - "{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc"
    - "{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc.cluster.local"
    - "*.{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc.cluster.local"
    - localhost
    - cb-done-service
    - "{{ .Values.domain }}"
    - "*.{{ .Values.domain }}"
    - "*.cb-1-service"
    - "cb-1-service-srv"
    - cb-1-service
    - "*.cb-2-service"
    - "cb-2-service-srv"
    - cb-2-service
    - "*.cb-3-service"
    - "cb-3-service-srv"
    - cb-3-service
    - "*.cb-4-service"
    - "cb-4-service-srv"
    - cb-4-service
    - "*.cb-5-service"
    - "cb-5-service-srv"
    - cb-5-service
    - "*.cb-6-service"
    - "cb-6-service-srv"
    - cb-6-service
    - "*.cb-7-service"
    - "cb-7-service-srv"
    - cb-7-service
  issuerRef:
    group: awspca.cert-manager.io
    name: pca-issuer
    kind: AWSPCAClusterIssuer
  duration: 8760h0m0s # 260d
  renewBefore: 720h0m0s # 30d
  secretName: couchbase-cert
  usages:
    - server auth
  privateKey:
    algorithm: RSA
    encoding: PKCS8
    size: 2048
