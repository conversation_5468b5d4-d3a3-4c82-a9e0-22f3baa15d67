---
kind: ConfigMap
apiVersion: v1
metadata:
  name: couchbase-rebalance-configuration-script
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
data:
  rebalance_setting.sh: |-
    # Wait for Couchbase cluster to be ready
    timeout {{ .Values.timeouts.couchbaseCli.rebalanceSettings }} bash -c 'until curl -k {{ include "couchbase-done.clusterServiceUrlHttps" . }} -u $ADMIN_USERNAME:$ADMIN_PASSWORD; do sleep 10; done'

    # Configure rebalance settings with retry logic
    for attempt in {1..3}; do
      echo "Attempt $attempt: Configuring rebalance settings"
      if couchbase-cli setting-rebalance \
        --cluster {{ include "couchbase-done.clusterServiceUrlSecure" . }} \
        --username=$ADMIN_USERNAME \
        --password=$ADMIN_PASSWORD \
        --set \
        --moves-per-node {{ .Values.cluster.cluster.rebalance.maxMovesPerNode }} \
        --no-ssl-verify; then
        echo "Successfully configured rebalance settings"
        exit 0
      else
        echo "Failed to configure rebalance settings, attempt $attempt"
        if [ $attempt -lt 3 ]; then
          sleep 5
        fi
      fi
    done
    echo "Failed to configure rebalance settings after 3 attempts"
    exit 1
---
apiVersion: batch/v1
kind: Job
metadata:
  name: couchbase-rebalance-configuration-job
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
  annotations:
    argocd.argoproj.io/hook: PostSync
spec:
  backoffLimit: {{ .Values.jobs.rebalanceConfiguration.backoffLimit }}
  activeDeadlineSeconds: {{ .Values.jobs.rebalanceConfiguration.activeDeadlineSeconds }}
  template:
    metadata:
      labels:
        {{- include "couchbase-done.selectorLabels" . | nindent 8 }}
    spec:
      containers:
      - command: [ "sh", "/opt/scripts/rebalance_setting.sh" ]
        image: {{ .Values.images.couchbaseServer.repository }}:{{ .Values.images.couchbaseServer.tag }}
        name: couchbase
        env:
        - name: ADMIN_USERNAME
          valueFrom:
            secretKeyRef:
              key: username
              name: couchbase-admin-secret
        - name: ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: couchbase-admin-secret
        volumeMounts:
        - name: rebalance-configuration-script
          mountPath: /opt/scripts
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
              - ALL
      restartPolicy: OnFailure
      volumes:
      - name: rebalance-configuration-script
        configMap:
          name: couchbase-rebalance-configuration-script
      tolerations:
        {{- include "couchbase-done.tolerations" . | nindent 8 }}
      affinity:
        {{- include "couchbase-done.affinity" . | nindent 8 }}
      nodeSelector:
        {{- include "couchbase-done.nodeSelector" . | nindent 8 }}
