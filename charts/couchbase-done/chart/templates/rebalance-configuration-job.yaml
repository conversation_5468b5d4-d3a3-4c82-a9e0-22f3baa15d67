---
kind: ConfigMap
apiVersion: v1
metadata:
  name: couchbase-rebalance-configuration-script
data:
  rebalance_setting.sh: |-
    timeout 5m bash -c 'until curl -k https://{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc.cluster.local:18091 -u $ADMIN_USERNAME:$ADMIN_PASSWORD; do sleep 10; done'
    couchbase-cli setting-rebalance \
      --cluster couchbases://{{ .Values.cluster.name }}-srv.{{ .Release.Namespace }}.svc.cluster.local \
      --username=$ADMIN_USERNAME \
      --password=$ADMIN_PASSWORD \
      --set \
      --moves-per-node {{ .Values.cluster.cluster.rebalance.maxMovesPerNode }} \
      --no-ssl-verify
---
apiVersion: batch/v1
kind: Job
metadata:
  name: couchbase-rebalance-configuration-job
  annotations:
    argocd.argoproj.io/hook: PostSync
spec:
  template:
    spec:
      containers:
      - command: [ "sh", "/opt/scripts/rebalance_setting.sh" ]
        image: {{ .Values.images.couchbaseServer.repository }}:{{ .Values.images.couchbaseServer.tag }}
        name: couchbase
        env:
        - name: ADMIN_USERNAME
          valueFrom:
            secretKeyRef:
              key: username
              name: couchbase-admin-secret
        - name: ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: couchbase-admin-secret
        volumeMounts:
        - name: rebalance-configuration-script
          mountPath: /opt/scripts
      restartPolicy: OnFailure
      volumes:
      - name: rebalance-configuration-script
        configMap:
          name: couchbase-rebalance-configuration-script
      tolerations:
      - key: "node.vodafone.com/provisioner"
        operator: "Equal"
        value: "cet-bottlerocket-couchbase"
        effect: "NoSchedule"
      - key: node.vodafone.com/arch
        operator: Equal
        value: arm64
      - key: node.vodafone.com/arch
        operator: Equal
        value: amd64
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node.vodafone.com/platform
                    operator: In
                    values:
                      - couchbase
                  - key: eks.amazonaws.com/compute-type
                    operator: NotIn
                    values:
                      - fargate
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - arm64
                      - amd64
                  - key: kubernetes.io/os
                    operator: In
                    values:
                      - linux
      nodeSelector: {}
