---
kind: ConfigMap
apiVersion: v1
metadata:
  name: couchbase-rebalance-configuration-script
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
data:
  rebalance_setting.sh: |-
    #!/bin/bash
    set -euo pipefail

    # Source common functions
    source /scripts/common-functions.sh

    # Validate required environment variables
    validate_env "ADMIN_USERNAME" "ADMIN_PASSWORD" || exit 1

    echo "Configuring Couchbase rebalance settings"
    echo "Max moves per node: {{ .Values.cluster.cluster.rebalance.maxMovesPerNode }}"

    # Wait for Couchbase cluster to be ready
    wait_for_couchbase "{{ .Values.timeouts.couchbaseCli.rebalanceSettings }}" || exit 1

    # Configure rebalance settings with retry logic
    retry_with_backoff 3 5 "
        couchbase-cli setting-rebalance \
          --cluster {{ include "couchbase-done.clusterServiceUrlSecure" . }} \
          --username=\$ADMIN_USERNAME \
          --password=\$ADMIN_PASSWORD \
          --set \
          --moves-per-node {{ .Values.cluster.cluster.rebalance.maxMovesPerNode }} \
          --no-ssl-verify
    " || exit 1

    echo "Successfully configured rebalance settings"
---
apiVersion: batch/v1
kind: Job
metadata:
  name: couchbase-rebalance-configuration-job
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
  annotations:
    argocd.argoproj.io/hook: PostSync
spec:
  backoffLimit: {{ .Values.jobs.rebalanceConfiguration.backoffLimit }}
  activeDeadlineSeconds: {{ .Values.jobs.rebalanceConfiguration.activeDeadlineSeconds }}
  template:
    metadata:
      labels:
        {{- include "couchbase-done.selectorLabels" . | nindent 8 }}
    spec:
      containers:
      - command: [ "sh", "/opt/scripts/rebalance_setting.sh" ]
        image: {{ .Values.images.couchbaseServer.repository }}:{{ .Values.images.couchbaseServer.tag }}
        name: couchbase
        env:
        - name: ADMIN_USERNAME
          valueFrom:
            secretKeyRef:
              key: username
              name: couchbase-admin-secret
        - name: ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: couchbase-admin-secret
        volumeMounts:
        - name: rebalance-configuration-script
          mountPath: /opt/scripts
        - name: common-scripts
          mountPath: /scripts
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
              - ALL
      restartPolicy: OnFailure
      volumes:
      - name: rebalance-configuration-script
        configMap:
          name: couchbase-rebalance-configuration-script
          defaultMode: 0755
      - name: common-scripts
        configMap:
          name: couchbase-scripts
          defaultMode: 0755
      tolerations:
        {{- include "couchbase-done.tolerations" . | nindent 8 }}
      affinity:
        {{- include "couchbase-done.affinity" . | nindent 8 }}
      nodeSelector:
        {{- include "couchbase-done.nodeSelector" . | nindent 8 }}
