---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-ingress
spec:
  # deny ingress to all pods in the namespace
  podSelector: {}
  policyTypes:
  - Ingress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-same-namespace
spec:
  # all ingress to all pods from the same namespace
  podSelector: {}
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          namespace-name: "{{ .Release.Namespace }}"
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-couchbase-admission-controller
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: couchbase-admission-controller
  policyTypes:
  - Ingress
  ingress:
  - from: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 8443
    - protocol: TCP
      port: 15090 # istio sidecar metrics prometheus endpoint
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-monitoring-couchbase-nodes
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: couchbase
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          namespace.vodafone.com/purpose: monitoring
    ports:
    - protocol: TCP
      port: 9091 # metrics sidecar prometheus endpoint
    - protocol: TCP
      port: 15090 # istio sidecar metrics prometheus endpoint
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-monitoring-couchbase-operator
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: couchbase-operator
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          namespace.vodafone.com/purpose: monitoring
    ports:
    - protocol: TCP
      port: 8383 # metrics prometheus endpoint
    - protocol: TCP
      port: 15090 # istio sidecar metrics prometheus endpoint
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-couchbase-clients
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: couchbase
  policyTypes:
  - Ingress
  ingress:
  - from: [] # later on we can restrict to only namespaces that actually connect to couchbase, and istio (for web ui)
    ports:   # https://docs.couchbase.com/server/current/install/install-ports.html
    - port: 9999
      protocol: TCP
    - port: 11206
      protocol: TCP
    - port: 11207
      protocol: TCP
    - port: 11211
      protocol: TCP
    - port: 18091
      protocol: TCP
    - port: 18092
      protocol: TCP
    - port: 18093
      protocol: TCP
    - port: 18094
      protocol: TCP
    - port: 18095
      protocol: TCP
    - port: 18096
      protocol: TCP
    - port: 18097
      protocol: TCP
    - port: 19102
      protocol: TCP
    - port: 19130
      protocol: TCP
    - port: 21150
      protocol: TCP
