---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: dr-couchbase-done-query
spec:
  host: couchbase-query-srv
  trafficPolicy:
    tls:
      mode: SIMPLE
      insecureSkipVerify: true
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: dr-couchbase-done
spec:
  host: '{{ .Values.cluster.name }}'
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    outlierDetection:
      baseEjectionTime: 3m
      consecutive5xxErrors: 1
      interval: 1s
      maxEjectionPercent: 100
    tls:
      mode: SIMPLE
      insecureSkipVerify: true
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: vs-couchbase-done-query
spec:
  hosts:
    - '{{ .Values.cluster.name }}-query.{{ .Values.subdomain }}'
  gateways:
  - istio-routing-platform/gateway-platform
  http:
    - match:
        - uri:
            exact: "/query/service"
      retries:
        attempts: 1
        perTryTimeout: 75s
      route:
        - destination:
            host: couchbase-query-srv
            port:
              number: 18093
      timeout: 75s
    - match:
        - uri:
            prefix: "/pools/default/buckets"
      retries:
        attempts: 1
        perTryTimeout: 75s
      route:
        - destination:
            host: couchbase-done
            port:
              number: 18091
      timeout: 75s
