---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: couchbase-admin-secret
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "couchbase-done.labels" . | nindent 4 }}
    app.kubernetes.io/component: secret-management
spec:
  refreshInterval: 5m0s
  secretStoreRef:
    name: ssm-couchbase-secret-store
  target:
    name: couchbase-admin-secret
    creationPolicy: Owner
  data:
    - secretKey: username
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/admin
        property: username
    - secretKey: password
      remoteRef:
        key: /aws/reference/secretsmanager/sol/component/couchbase-done/couchbase/admin
        property: password
