# Couchbase Done Helm Chart

This Helm chart deploys a Couchbase cluster with comprehensive configuration options for production environments.

## Overview

The chart includes:
- Couchbase Operator and Server deployment
- Certificate management with cert-manager
- External secrets integration with AWS Parameter Store
- User management and RBAC configuration
- Backup and snapshot scheduling
- Network policies and security configurations
- Monitoring and logging setup

## Configuration

### Core Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `domain` | Primary domain for the cluster | `REPLACE_ME` |
| `subdomain` | Subdomain for services | `REPLACE_ME` |
| `cluster.name` | Name of the Couchbase cluster | `couchbase-done` |

### AWS Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `aws.region` | AWS region for resources | `eu-central-1` |
| `externalSecrets.roleArn` | IAM role ARN for external secrets | `REPLACE_ME` |

### Certificate Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `certificates.issuer` | Certificate issuer name | `pca-issuer` |
| `certificates.issuerGroup` | Certificate issuer group | `awspca.cert-manager.io` |
| `certificates.issuerKind` | Certificate issuer kind | `AWSPCAClusterIssuer` |
| `certificates.duration` | Certificate duration | `8760h0m0s` |
| `certificates.renewBefore` | Certificate renewal time | `720h0m0s` |
| `certificates.additionalDnsNames` | Additional DNS names for certificates | `[]` |

### User Management

Users are configured in the `users` section. Each user requires:
- `secretName`: Name of the Kubernetes secret containing credentials
- `roles`: Couchbase RBAC roles for the user
- `description`: Human-readable description of the user's purpose

### Job Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `jobs.backoffLimit` | Default backoff limit for jobs | `3` |
| `jobs.userCreation.backoffLimit` | Backoff limit for user creation jobs | `3` |
| `jobs.userCreation.activeDeadlineSeconds` | Timeout for user creation jobs | `600` |
| `jobs.rebalanceConfiguration.backoffLimit` | Backoff limit for rebalance jobs | `3` |
| `jobs.rebalanceConfiguration.activeDeadlineSeconds` | Timeout for rebalance jobs | `300` |

### Timeout Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `timeouts.virtualService.queryService` | Query service timeout | `75s` |
| `timeouts.virtualService.bucketsApi` | Buckets API timeout | `75s` |
| `timeouts.virtualService.retryAttempts` | Retry attempts for virtual service | `1` |
| `timeouts.couchbaseCli.defaultTimeout` | Default CLI timeout | `5m` |
| `timeouts.couchbaseCli.userManagement` | User management CLI timeout | `2m` |
| `timeouts.couchbaseCli.rebalanceSettings` | Rebalance settings CLI timeout | `1m` |

### Backup Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `backups.full.schedule` | Full backup schedule (cron) | `0 3 * * 0` |
| `backups.incremental.schedule` | Incremental backup schedule (cron) | `0 3 * * 1-6` |
| `backups.size` | Backup storage size | `50Gi` |
| `backups.threads` | Number of backup threads | `4` |

### Snapshot Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `snapshotSchedule.schedule` | Snapshot schedule (cron) | `0 5 * * *` |
| `snapshotSchedule.retentionExpiry` | Snapshot retention time | `150h` |
| `snapshotSchedule.retentionMaxCount` | Maximum number of snapshots | `5` |

## Security Features

### Network Policies
- Default deny ingress policy
- Allow same-namespace communication
- Specific policies for Couchbase components
- Monitoring namespace access

### Security Contexts
- Non-root containers where possible
- Read-only root filesystems
- Dropped capabilities
- Privilege escalation prevention

### Certificate Management
- Automatic certificate generation and renewal
- Configurable certificate authorities
- Support for additional DNS names

## Deployment

```bash
# Install the chart
helm install couchbase-done ./chart -f values.yaml

# Upgrade the chart
helm upgrade couchbase-done ./chart -f values.yaml

# Uninstall the chart
helm uninstall couchbase-done
```

## Troubleshooting

### Common Issues

1. **Certificate Issues**: Check cert-manager logs and certificate status
2. **User Creation Failures**: Verify external secrets and Couchbase connectivity
3. **Backup Failures**: Check storage permissions and backup job logs
4. **Network Connectivity**: Verify network policies and service configurations

### Useful Commands

```bash
# Check certificate status
kubectl get certificates

# Check external secrets
kubectl get externalsecrets

# Check job status
kubectl get jobs

# View logs
kubectl logs -l app.kubernetes.io/name=couchbase-done
```

## Architecture

The chart follows Kubernetes best practices:
- Separation of concerns with multiple templates
- Reusable helper templates in `_helpers.tpl`
- Comprehensive labeling for resource management
- Security-first approach with minimal privileges
- Configurable timeouts and retry logic
- Proper resource management and limits

## Contributing

When modifying this chart:
1. Update this README for any new configuration options
2. Follow Helm best practices
3. Test changes thoroughly
4. Update version numbers appropriately
5. Document breaking changes
