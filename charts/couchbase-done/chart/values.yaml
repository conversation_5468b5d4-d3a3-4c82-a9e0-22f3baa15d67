domain: REPLACE_ME
subdomain: REPLACE_ME

# AWS configuration
aws:
  region: eu-central-1

# Certificate configuration
certificates:
  issuer: pca-issuer
  issuerGroup: awspca.cert-manager.io
  issuerKind: AWSPCAClusterIssuer
  duration: 8760h0m0s # 260d
  renewBefore: 720h0m0s # 30d
  # Additional DNS names for certificates (beyond the auto-generated ones)
  additionalDnsNames: []

externalSecrets:
  roleArn: REPLACE_ME

images:
  couchbaseOperator:
    repository: "196433213517.dkr.ecr.eu-central-1.amazonaws.com/k8s-modules-sol-done/couchbase/operator"
    tag: "2.8.0-r1"
  couchbaseServer:
    repository: "196433213517.dkr.ecr.eu-central-1.amazonaws.com/k8s-modules-sol-done/couchbase/couchbase"
    tag: "7.6.6-r1"
  fluentBit:
    repository: "196433213517.dkr.ecr.eu-central-1.amazonaws.com/k8s-modules-sol-done/couchbase/fluent-bit"
    tag: "1.2.9-r1"
  cbBackup:
    repository: "196433213517.dkr.ecr.eu-central-1.amazonaws.com/k8s-modules-sol-done/couchbase/operator-backup"
    tag: "1.4.1-r1"
  secretReloader:
    repository: "196433213517.dkr.ecr.eu-central-1.amazonaws.com/k8s-modules-sol-done/couchbase/secret-reloader"
    tag: "7.6.6-r1"

users:
  monitoring:
    secretName: couchbase-monitoring-secret
    roles: data_reader[*],data_monitoring[*],external_stats_reader
  couchbase-service-broker:
    secretName: couchbase-couchbase-service-broker-secret
    roles: bucket_admin[*],cluster_admin,security_admin_local,query_select[*],query_update[*],query_delete[*],query_insert[*],query_manage_index[*]
  batch-migration:
    secretName: couchbase-batch-migration-secret
    roles: data_writer[*],data_reader[*],bucket_admin[*],query_select[*],query_manage_index[*],query_update[*],query_delete[*],query_insert[*],views_reader[*],data_dcp_reader[*]
  test-data-management:
    secretName: couchbase-test-data-management-secret
    roles: query_insert[rtb_CB1:RTBCustomerBill:acbrimpactevent],query_insert[rtb_CB1:RTBCustomerBill:computeacbr],query_insert[rtb_CB1:RTBCustomerBill:falloutentity],query_insert[rtb_CB1:VFDERTBCustomerBill:futuretermination],query_insert[rtb_CB2:RTBCustomerBill:appliedcustomerbillingrate],query_select[rtb_CB1:RTBCustomerBill:acbrimpactevent],query_select[rtb_CB1:RTBCustomerBill:computeacbr],query_select[rtb_CB1:RTBCustomerBill:falloutentity],query_select[rtb_CB1:VFDERTBCustomerBill:futuretermination],query_select[rtb_CB2:RTBCustomerBill:appliedcustomerbillingrate]
  read-only-data:
    secretName: couchbase-read-only-data-secret
    roles: views_reader[*],query_select[*],data_reader[*]
  mediation-rating-billing-service:
    secretName: couchbase-mediation-rating-billing-service-secret
    roles: query_select[*]

secretReloader:
  tolerations:
  - key: "node.vodafone.com/provisioner"
    operator: "Equal"
    value: "cet-bottlerocket-couchbase"
    effect: "NoSchedule"
  - key: node.vodafone.com/arch
    operator: Equal
    value: arm64
  - key: node.vodafone.com/arch
    operator: Equal
    value: amd64
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: node.vodafone.com/platform
                operator: In
                values:
                  - couchbase
              - key: eks.amazonaws.com/compute-type
                operator: NotIn
                values:
                  - fargate
              - key: kubernetes.io/arch
                operator: In
                values:
                  - arm64
                  - amd64
              - key: kubernetes.io/os
                operator: In
                values:
                  - linux
  nodeSelector: {}

backups:
  full:
    schedule: "0 3 * * 0"
  incremental:
    schedule: "0 3 * * 1-6"
  size: 50Gi
  threads: 4

couchbaseOperator:
  tolerations:
  - key: "node.vodafone.com/provisioner"
    operator: "Equal"
    value: "cet-bottlerocket-couchbase"
    effect: "NoSchedule"
  - key: node.vodafone.com/arch
    operator: Equal
    value: arm64
  - key: node.vodafone.com/arch
    operator: Equal
    value: amd64
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: node.vodafone.com/platform
                operator: In
                values:
                  - couchbase
              - key: eks.amazonaws.com/compute-type
                operator: NotIn
                values:
                  - fargate
              - key: kubernetes.io/arch
                operator: In
                values:
                  - arm64
                  - amd64
              - key: kubernetes.io/os
                operator: In
                values:
                  - linux
  nodeSelector: {}

cluster:
  name: couchbase-done
  cluster:
    autoFailoverTimeout: 5s
    autoFailoverMaxCount: 5
    data:
      minReplicasCount: 0
    rebalance:
      maxMovesPerNode: 24
    dataServiceMemoryQuota: 44Gi
    indexServiceMemoryQuota: 19Gi
    queryServiceMemoryQuota: 6Gi
  backup:
    roleArn: REPLACE_ME
    resources:
      requests:
        cpu: '2'
        memory: 4Gi
      limits:
        cpu: '4'
        memory: 10Gi
    tolerations:
    - key: "node.vodafone.com/provisioner"
      operator: "Equal"
      value: "cet-bottlerocket-couchbase"
      effect: "NoSchedule"
    - key: node.vodafone.com/arch
      operator: Equal
      value: arm64
    - key: node.vodafone.com/arch
      operator: Equal
      value: amd64
    nodeSelector:
      node.vodafone.com/platform: couchbase
  logging:
    audit:
      enabled: false
      disabledEvents: []
      rotation:
        interval: 15m
        pruneAge: 0m
        size: 20Mi
    server:
      enabled: true
      sidecar:
        resources:
          requests:
            cpu: 100m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 256Mi
  servers:
    data:
      size: 3
      storage:
        size: 100Gi
      resources:
        requests:
          cpu: '4'
          memory: 50Gi
        limits:
          cpu: '8'
          memory: 55Gi
      tolerations:
      - key: "node.vodafone.com/provisioner"
        operator: "Equal"
        value: "cet-bottlerocket-couchbase"
        effect: "NoSchedule"
      - key: node.vodafone.com/arch
        operator: Equal
        value: arm64
      - key: node.vodafone.com/arch
        operator: Equal
        value: amd64
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node.vodafone.com/platform
                    operator: In
                    values:
                      - couchbase
                  - key: eks.amazonaws.com/compute-type
                    operator: NotIn
                    values:
                      - fargate
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - arm64
                      - amd64
                  - key: kubernetes.io/os
                    operator: In
                    values:
                      - linux
      nodeSelector: {}
    index:
      size: 2
      storage:
        size: 20Gi
      resources:
        requests:
          cpu: '4'
          memory: 21Gi
        limits:
          cpu: '8'
          memory: 24Gi
      tolerations:
      - key: "node.vodafone.com/provisioner"
        operator: "Equal"
        value: "cet-bottlerocket-couchbase"
        effect: "NoSchedule"
      - key: node.vodafone.com/arch
        operator: Equal
        value: arm64
      - key: node.vodafone.com/arch
        operator: Equal
        value: amd64
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node.vodafone.com/platform
                    operator: In
                    values:
                      - couchbase
                  - key: eks.amazonaws.com/compute-type
                    operator: NotIn
                    values:
                      - fargate
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - arm64
                      - amd64
                  - key: kubernetes.io/os
                    operator: In
                    values:
                      - linux
      nodeSelector: {}
    query:
      size: 2
      storage:
        size: 20Gi
      resources:
        requests:
          cpu: '2'
          memory: 8Gi
        limits:
          cpu: '4'
          memory: 10Gi
      tolerations:
      - key: "node.vodafone.com/provisioner"
        operator: "Equal"
        value: "cet-bottlerocket-couchbase"
        effect: "NoSchedule"
      - key: node.vodafone.com/arch
        operator: Equal
        value: arm64
      - key: node.vodafone.com/arch
        operator: Equal
        value: amd64
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node.vodafone.com/platform
                    operator: In
                    values:
                      - couchbase
                  - key: eks.amazonaws.com/compute-type
                    operator: NotIn
                    values:
                      - fargate
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - arm64
                      - amd64
                  - key: kubernetes.io/os
                    operator: In
                    values:
                      - linux
      nodeSelector: {}
  networking:
    exposeAdminConsole: true
    disableHttpUi: true
    disableHttpsUi: true
    tls:
      nodeToNodeEncryption: Strict  # Can be Strict, All, or Control
# Backup PVC tagging configuration
tagBackupPvcCron:
  schedule: "0 0 * * *"
  image:
    repository: "196433213517.dkr.ecr.eu-central-1.amazonaws.com/k8s-modules-sol-done/alpine/k8s"
    tag: "v1.33.3"
    pullPolicy: "IfNotPresent"

# Snapshot schedule configuration
snapshotSchedule:
  schedule: "0 5 * * *"
  retentionExpiry: 150h
  retentionMaxCount: 5

# Job configuration for error handling and robustness
jobs:
  # Default timeout for waiting for Couchbase to be ready
  couchbaseReadyTimeout: "5m"
  # Default backoff limit for jobs
  backoffLimit: 3
  # Retry configuration for user creation jobs
  userCreation:
    backoffLimit: 3
    activeDeadlineSeconds: 600  # 10 minutes
  # Retry configuration for rebalance configuration job
  rebalanceConfiguration:
    backoffLimit: 3
    activeDeadlineSeconds: 300  # 5 minutes

# Timeout configurations
timeouts:
  # HTTP request timeouts for virtual service
  virtualService:
    queryService: 75s
    bucketsApi: 75s
    retryAttempts: 1
  # CLI operation timeouts
  couchbaseCli:
    defaultTimeout: "5m"
    userManagement: "2m"
    rebalanceSettings: "1m"
