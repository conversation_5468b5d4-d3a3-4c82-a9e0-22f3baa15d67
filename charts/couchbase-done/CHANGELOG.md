# Changelog


<a name="couchbase-done/v1.22.0"></a>
## [couchbase-done/v1.22.0] - 2025-08-13

### :sparkles: Features
- [`b3910c7`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b3910c7672acaec2c82ac968e0003d39c610871d) - **couchbase-done:** matching query service selector


<a name="couchbase-done/v1.21.0"></a>
## [couchbase-done/v1.21.0] - 2025-08-12

### :sparkles: Features
- [`b716b08`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b716b08b71dd4d9ff8cde3db24b173d9b348c444) - **couchbase-done:** bump alpine image to v1.33.3 and rightsize resources


<a name="couchbase-done/v1.20.0"></a>
## [couchbase-done/v1.20.0] - 2025-07-16

### :sparkles: Features
- [`b5d05fc`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b5d05fcb9231011c95f7eab854d016d2e2020f81) - **couchbase-done:** remove istio sidecar injection annotations
- [`ff1c384`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/ff1c384bef74faa7d32fa6a7b2c74c12b3e3e85b) - **couchbase-done:** increase resources
- [`ed9d053`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/ed9d053261351dbffa2d0f3a1b9602110c0a37c3) - **couchbase-done:** divide index and query nodes


<a name="couchbase-done/v1.19.0"></a>
## [couchbase-done/v1.19.0] - 2025-07-14

### :sparkles: Features
- [`66efcdf`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/66efcdfe83a44a64414b0b0ac53f83b10a7b29ca) - **couchbase-done:** arm64 compatibility for jobs


<a name="couchbase-done/v1.18.0"></a>
## [couchbase-done/v1.18.0] - 2025-07-02

### :sparkles: Features
- [`f39c67f`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/f39c67fe359df30b7cd3308b778a811bbcca7f12) - **couchbase-done:** add mediation-rating-billing-service user


<a name="couchbase-done/v1.17.0"></a>
## [couchbase-done/v1.17.0] - 2025-06-16

### :sparkles: Features
- [`ce330c4`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/ce330c42ae90d43096bee2092b0a8cd025155e38) - **couchbase-done:** arm64 compatibility


<a name="couchbase-done/v1.16.0"></a>
## [couchbase-done/v1.16.0] - 2025-06-12

### :sparkles: Features
- [`17a775a`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/17a775a25bb9cd610e5b43c544c165e27bda0002) - **couchbase-done:** remove migration block


<a name="couchbase-done/v1.15.0"></a>
## [couchbase-done/v1.15.0] - 2025-05-26

### :sparkles: Features
- [`d693557`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d693557251b9c16201d460203a64d7749414cc7c) - **couchbase-done:** bump couchbase server and secret reloader to 7.6.6


<a name="couchbase-done/v1.14.1"></a>
## [couchbase-done/v1.14.1] - 2025-05-20

### :beetle: Bug Fixes
- [`aed99bb`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/aed99bb0be2431354284fac5ce725562a7352b32) - **couchbase-done:** fixed backup job security context


<a name="couchbase-done/v1.14.0"></a>
## [couchbase-done/v1.14.0] - 2025-05-13

### :sparkles: Features
- [`39405bc`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/39405bcd0aad035920af5ee5211d89e46d8d9473) - **couchbase-done:** add read-only-data role


<a name="couchbase-done/v1.13.0"></a>
## [couchbase-done/v1.13.0] - 2025-05-06

### :sparkles: Features
- [`f7ae951`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/f7ae951358b3699e98af3dcd9bed551f8642aee6) - **couchbase-done:** bump helm chart to 2.80.0


<a name="couchbase-done/v1.12.0"></a>
## [couchbase-done/v1.12.0] - 2025-01-31

### :sparkles: Features
- [`fca907d`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fca907d96da5a310e04fc86b58629d832fdef9f7) - **couchbase-done:** fix fluent-bit config and include audit logs


<a name="couchbase-done/v1.11.0"></a>
## [couchbase-done/v1.11.0] - 2025-01-21

### :sparkles: Features
- [`cbd13e1`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/cbd13e1d3266741b13cc195cc4685c2af72c8e89) - **couchbase-done:** set couchbase server and secret reloader tag to 7.6.3
- [`1fe60d5`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/1fe60d5cc5386afdd13e9bc878377acbb702d08b) - **couchbase-done:** make networking parameters configurable


<a name="couchbase-done/v1.10.0"></a>
## [couchbase-done/v1.10.0] - 2025-01-09

### :sparkles: Features
- [`9afecac`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/9afecacdbf97e5820c820ccb23b8a5a0ec710fec) - **couchbase-done:** upgrade couchbase-server and secret-reloader to 7.6.4


<a name="couchbase-done/v1.9.0"></a>
## [couchbase-done/v1.9.0] - 2024-12-09

### :sparkles: Features
- [`2e2c955`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/2e2c95589bbe8f7a9000f4069748dcbb0da987af) - **couchbase-done:** increase query service timeouts


<a name="couchbase-done/v1.8.0"></a>
## [couchbase-done/v1.8.0] - 2024-11-27

### :sparkles: Features
- [`e386ad6`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/e386ad6a50981d36d41161390eab72e656a06f3e) - **couchbase-done:** update snapshotClassName
- [`3aa1f76`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/3aa1f76aab3306c22ff9fdb76896ffa1d868d97c) - **couchbase-done:** add roles for test-data-management user
- [`61437bd`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/61437bdc45ecbc234549773d7d435e7f806927c4) - **couchbase-done:** add roles for test-data-management user
- [`d6e6faa`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d6e6faa86500fe7c158ce1db30a558b411f55c83) - **couchbase-done:** add roles for test-data-management user
- [`b0009ef`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b0009ef230907799db3d015159a690bf65568b4a) - **couchbase-done:** add roles for test-data-management user
- [`fbc9d9c`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fbc9d9c1572c516d760ee2ff0972a70edade45ca) - **couchbase-done:** add roles for test-data-management user
- [`4c8636e`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/4c8636ec0d94e397b9f71724e437875e744c1107) - **couchbase-done:** add roles for test-data-management user
- [`5164d30`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/5164d30dc999256ae88e9767ed393a9b125cfd83) - **couchbase-done:** add roles for test-data-management user
- [`2d05948`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/2d059489f200e972f1c67048a081764cf7c1e4ef) - **couchbase-done:** add roles for test-data-management user
- [`b5f8530`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b5f853029241852a107d26be7edd548c8068ffd5) - **couchbase-done:** add roles for test-data-management user


<a name="couchbase-done/v1.6.1"></a>
## [couchbase-done/v1.6.1] - 2024-11-15


<a name="couchbase-done/v1.7.0"></a>
## [couchbase-done/v1.7.0] - 2024-10-30

### :sparkles: Features
- [`3aa1f76`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/3aa1f76aab3306c22ff9fdb76896ffa1d868d97c) - **couchbase-done:** add roles for test-data-management user
- [`61437bd`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/61437bdc45ecbc234549773d7d435e7f806927c4) - **couchbase-done:** add roles for test-data-management user
- [`d6e6faa`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d6e6faa86500fe7c158ce1db30a558b411f55c83) - **couchbase-done:** add roles for test-data-management user
- [`b0009ef`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b0009ef230907799db3d015159a690bf65568b4a) - **couchbase-done:** add roles for test-data-management user
- [`fbc9d9c`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fbc9d9c1572c516d760ee2ff0972a70edade45ca) - **couchbase-done:** add roles for test-data-management user
- [`4c8636e`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/4c8636ec0d94e397b9f71724e437875e744c1107) - **couchbase-done:** add roles for test-data-management user
- [`5164d30`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/5164d30dc999256ae88e9767ed393a9b125cfd83) - **couchbase-done:** add roles for test-data-management user
- [`2d05948`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/2d059489f200e972f1c67048a081764cf7c1e4ef) - **couchbase-done:** add roles for test-data-management user
- [`b5f8530`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b5f853029241852a107d26be7edd548c8068ffd5) - **couchbase-done:** add roles for test-data-management user
- [`029a377`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/029a377dd34cec0de0aaa64df0067a0ca9e48071) - **couchbase-done:** disable autoResourceAllocation
- [`efc13a2`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/efc13a273885d7968d820ccbce084b8921def77e) - **couchbase-done:** remove cloudNativegateway feature
- [`cbbc4f6`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/cbbc4f6d86d05fa0a89c0430ba14ae3b849589c7) - **couchbase-done:** updated operator base chart to 2.70.0 and default values


<a name="couchbase-done/v1.5.1"></a>
## [couchbase-done/v1.5.1] - 2024-10-14


<a name="couchbase-done/v1.6.0"></a>
## [couchbase-done/v1.6.0] - 2024-10-14

### :sparkles: Features
- [`efc13a2`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/efc13a273885d7968d820ccbce084b8921def77e) - **couchbase-done:** remove cloudNativegateway feature
- [`cbbc4f6`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/cbbc4f6d86d05fa0a89c0430ba14ae3b849589c7) - **couchbase-done:** updated operator base chart to 2.70.0 and default values
- [`34c7fae`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/34c7faec11c44cb5ef1e97adfe1720facb6ad3fd) - **couchbase-done:** set insecureSkipVerify for destination rules


<a name="couchbase-done/v1.5.0"></a>
## [couchbase-done/v1.5.0] - 2024-09-17

### :sparkles: Features
- [`44b52e1`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/44b52e1db2e1c5309fc981b1976ed464777ba2c2) - **couchbase-done:** tmp add back dns names cb-1 to cb-7 in certificate
- [`a7543d5`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/a7543d5d854af2adbd0847a44a1f11d04bb5b1d8) - **couchbase-done:** add new http match to query virtualservice
- [`4698789`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/46987899ee4a2497bc7dee32f7f1386e90acbc39) - **couchbase-done:** bump operator chart version to 2.64.1
- [`77aec36`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/77aec36d950bbeeff1414566ec23e499e1998eac) - **couchbase-done:** remove unsecure ports from ingress networkpolicy
- [`58854e1`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/58854e148e7979a362af21262ff5a8b6e49dc4ae) - **couchbase-done:** remove couchbase ui virtualservice
- [`50ba8a8`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/50ba8a8ac45295596e6b6c90aa329d7ad4c1af42) - **couchbase-done:** remove obsolete dns names
- [`0b31943`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/0b3194367e137321fd25033e5e3198fbac63164e) - **couchbase-done:** remove servicemonitor


<a name="couchbase-done/v1.4.3"></a>
## [couchbase-done/v1.4.3] - 2024-08-27

### :sparkles: Features
- [`aa4c578`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/aa4c5780f5b5a4014aa046de750c05fb45e84acd) - **couchbase-done:** update security context for secret-reloader


<a name="couchbase-done/v1.4.2"></a>
## [couchbase-done/v1.4.2] - 2024-08-27

### :sparkles: Features
- [`bafd885`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/bafd88534ae1571bc6faf094fe4c9b534302a546) - **couchbase-done:** use secure query port


<a name="couchbase-done/v1.4.1"></a>
## [couchbase-done/v1.4.1] - 2024-08-23

### :sparkles: Features
- [`e9ab3ec`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/e9ab3ec15ee70e7b27dfedce508460cd0e328c99) - **couchbase-done:** virtual service timeout increase


<a name="couchbase-done/v1.4.0"></a>
## [couchbase-done/v1.4.0] - 2024-08-20

### :sparkles: Features
- [`bc47354`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/bc473546144bb3dc113d23bfc63b62ddaa3e3d2d) - **couchbase-done:** add node to node encryption
- [`a904c45`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/a904c45d00e9a7f9606dde5268b50fe4b31f216e) - **couchbase-done:** added security context for secret-reloader


<a name="couchbase-done/v1.3.2"></a>
## [couchbase-done/v1.3.2] - 2024-08-01

### :sparkles: Features
- [`59068ee`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/59068ee526305d8c2d11fcb5f52a2abec4d6a779) - **couchbase-done:** made rotateInterval and rotateSize configurable


<a name="couchbase-done/v1.3.1"></a>
## [couchbase-done/v1.3.1] - 2024-07-25

### :beetle: Bug Fixes
- [`7b5e7dd`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/7b5e7dd391082db6fa256504eb59c7b02b828621) - **couchbase-done:** fix fluent-bit repository


<a name="couchbase-done/v1.3.0"></a>
## [couchbase-done/v1.3.0] - 2024-07-25

### :sparkles: Features
- [`5c9deeb`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/5c9deeb41414a3670fde28741ee3cf678f7fc893) - **couchbase-done:** pull images from ecr


<a name="couchbase-done/v1.2.1"></a>
## [couchbase-done/v1.2.1] - 2024-07-08

### :sparkles: Features
- [`9d07b49`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/9d07b495b6096380561f3aacb1ad60674f21abe9) - **couchbase-done:** added dedicated query service


<a name="couchbase-done/v1.2.0"></a>
## [couchbase-done/v1.2.0] - 2024-06-17

### :sparkles: Features
- [`0d5e8fc`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/0d5e8fcf6764db5e0c481b33972a0bd172cb74c9) - **couchbase-done:** added tolerations and nodeselector for custom karpenter nodepool


<a name="couchbase-done/v1.1.0"></a>
## [couchbase-done/v1.1.0] - 2024-05-22

### :sparkles: Features
- [`e085d33`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/e085d337bc3ccace56e80d540f90c53c69bdaf3d) - **couchbase-done:** updated operator base chart to 2.62.0


<a name="couchbase-done/v1.0.4"></a>
## [couchbase-done/v1.0.4] - 2024-05-20

### :sparkles: Features
- [`21cc37f`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/21cc37f363f1fa8219c3ca73feed021d26a6ff42) - **couchbase-done:** update cb-server v7.2.5
- [`e92d183`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/e92d1836407dd34b16b5e08b887be7123e866bf6) - **couchbase-done:** update disabledEvents syntax
- [`46da96e`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/46da96ec93182e58b15e7aab44b0ea831ef372fb) - **couchbase-done:** upgrade helm chart version 2.61.0


<a name="couchbase-done/v1.0.3"></a>
## [couchbase-done/v1.0.3] - 2024-04-18

### :sparkles: Features
- [`185b1f7`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/185b1f7bf5a924f257d5a09015813b8d0210633b) - **couchbase-done:** added karpenter.sh/do-not-disrupt annotation


<a name="couchbase-done/v1.0.2"></a>
## [couchbase-done/v1.0.2] - 2024-04-12

### :sparkles: Features
- [`728b828`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/728b8282e1a9e2e22f2c327451fe55a7512849f2) - **couchbase-done:** removed datadog


<a name="couchbase-done/v1.0.1"></a>
## [couchbase-done/v1.0.1] - 2024-04-09

### :sparkles: Features
- [`85d12dc`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/85d12dc0393fe5cbdaea08a009ee63e8864ae777) - **couchbase-done:** enabled snapshotschedule


<a name="couchbase-done/v1.0.0"></a>
## [couchbase-done/v1.0.0] - 2024-02-21

### BREAKING CHANGE

module has been refactored for cet-eks compatibility

### :sparkles: Features
- [`b172d87`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b172d87d70c1436960ed308c36b15b4a70f78cdd) - **couchbase-done:** refactor chart for cet-eks migration


<a name="couchbase-done/v0.0.13"></a>
## [couchbase-done/v0.0.13] - 2024-01-10

### :sparkles: Features
- [`b8ad99f`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b8ad99f32b120ed814a9f07f0713d51fe2d9d174) - **couchbase-done:** updating batch-migration user roles


<a name="couchbase-done/v0.0.12"></a>
## [couchbase-done/v0.0.12] - 2023-12-07

### :sparkles: Features
- [`26561ef`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/26561ef13a1dfe8cf8b58978442107862aec8382) - **couchbase-done:** update compaction window


<a name="couchbase-done/v0.0.11"></a>
## [couchbase-done/v0.0.11] - 2023-11-23

### :sparkles: Features
- [`f457ee6`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/f457ee68f3ea691a93985f2e1f387b5b46d6091d) - **couchbase-done:** updating batch-migration user roles


<a name="couchbase-done/v0.0.10"></a>
## [couchbase-done/v0.0.10] - 2023-11-15

### :sparkles: Features
- [`d9c03bc`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d9c03bc72037aa5c9d027b9086d98f3a2c0ca6de) - **couchbase-done:** changed istio destinationrule
- [`0f327ee`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/0f327eef76be0c1418eece9cc14002a28f27f868) - **couchbase-done:** added istio destinationrule


<a name="couchbase-done/v0.0.9"></a>
## [couchbase-done/v0.0.9] - 2023-11-14

### :sparkles: Features
- [`07964c0`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/07964c0eb0c09fa95d140477a769b02be7d7e956) - **couchbase-done:** added query virtual service


<a name="couchbase-done/v0.0.8"></a>
## [couchbase-done/v0.0.8] - 2023-11-09

### :sparkles: Features
- [`c55e9c8`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/c55e9c8e2862a88caae79905ddaeadb58d76c894) - **couchbase-done:** add user for batch migration


<a name="couchbase-done/v0.0.7"></a>
## [couchbase-done/v0.0.7] - 2023-10-17

### :sparkles: Features
- [`a9739d0`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/a9739d0b9585b156ede6e08d1f144cbcf56bfdc2) - **couchbase-done:** optimizing CSB user roles


<a name="couchbase-done/v0.0.6"></a>
## [couchbase-done/v0.0.6] - 2023-10-13

### :sparkles: Features
- [`0ae4c02`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/0ae4c023ba1a141608b6eaee17c5e41c6212235f) - **couchbase-done:** optimizing CSB user roles


<a name="couchbase-done/v0.0.5"></a>
## [couchbase-done/v0.0.5] - 2023-09-15

### :sparkles: Features
- [`f05cc4e`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/f05cc4e1d6551f75c0a7aad6383b0692d0fb370b) - **couchbase-done:** updated to cb-server tag to 7.1.5-r1
- [`0e28ab0`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/0e28ab05f451317cc8d46c874954e1a1ee190b41) - **couchbase-done:** update ecr repo
- [`7e2ee04`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/7e2ee0419ecbb0efbce03047da9502e4ad2a5175) - **couchbase-done:** change to ecr repo for cb-server


<a name="couchbase-done/v0.0.4"></a>
## [couchbase-done/v0.0.4] - 2023-08-03

### :sparkles: Features
- [`870abde`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/870abde812a87e33cd9cdb28febf033540cbcf42) - **couchbase-done:** updated default values. made audit logging configurable
- [`94e4f4b`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/94e4f4b89ee3f7d16a1e810eb569e4902e533c18) - **couchbase-done:** updated operator base chart to 2.42.1


<a name="couchbase-done/v0.0.3"></a>
## [couchbase-done/v0.0.3] - 2023-06-14

### :sparkles: Features
- [`d1f23a8`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d1f23a879f417a4ed4bdd2595a2e133e6f9526ad) - **couchbase-done:** update SAN entries in Certificate


<a name="couchbase-done/v0.0.2"></a>
## [couchbase-done/v0.0.2] - 2023-06-12

### :sparkles: Features
- [`fc0b12e`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fc0b12ef65567d48cb8224d5bd22ce77f7028ee0) - **couchbase-done:** correcting cb service broker user roles privileges
- [`9a58e4b`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/9a58e4bb9cc875ac1ac52821106599d771213c02) - **couchbase-done:** updating cb service broker user roles privileges
- [`80ab60b`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/80ab60b6775ffec7d9ce913312076890e46cacc2) - **couchbase-done:** cb-done improvements for cluster and indexer


<a name="couchbase-done/v0.0.1"></a>
## couchbase-done/v0.0.1 - 2023-05-08

### :sparkles: Features
- [`d9a38b7`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d9a38b7e425bc1ef775246e6f8af75fa8d082c00) - **couchbase-done:** changed certificate dns name
- [`6d74a29`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/6d74a290d0b3cf02f65aeaec5789f8afe79c78bf) - **couchbase-done:** made cluster storage size configurable
- [`fed570a`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fed570a8a08a582edafdb43de34a8886ee640e7a) - **couchbase-done:** exclude admin from list of secrets
- [`6ae7bc2`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/6ae7bc2f022247a1591c0d956608769faf6eb6bb) - **couchbase-done:** added list of secrets
- [`fedc5eb`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fedc5eb7972f9d0735476a8b717b446dffec5b27) - **couchbase-done:** addedm timestamp output to secret-reloader
- [`d557a23`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d557a235200a4cfcbfc8320656a62c5600c06774) - **couchbase-done:** using helm templating for externalsecrets
- [`fc7cc70`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fc7cc70e8644603fa87297f0bd89bc988f15fdf2) - **couchbase-done:** using helm templating for externalsecrets
- [`7a6272c`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/7a6272c2307787dfa88b402dd33f09e814b13bf5) - **couchbase-done:** using helm templating for user create job definition
- [`bc4ae5a`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/bc4ae5af3bad29d8d810759980b177591772af8d) - **couchbase-done:** made volume mapping for secret-reloader more generic
- [`58a50ce`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/58a50ceefaf5c623908d4b5fccaf7b040477350f) - **couchbase-done:** code cleanup
- [`617e7c3`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/617e7c30ba2e1427e74799566afe816006ea57d0) - **couchbase-done:** code cleanup
- [`fdac0a4`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fdac0a48c182a66b83cc7ac44387a754e7f31203) - **couchbase-done:** code cleanup
- [`d445b9a`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d445b9a248f02a3010bf734a13a36fe81f1e3a47) - **couchbase-done:** testing one create user configmap solution
- [`9e6c448`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/9e6c4485a7991ecd1f4bef90d55633b25612fecc) - **couchbase-done:** removed old couchbase secret from external secrets
- [`7957163`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/7957163f301fe175125e659ad54294646e82d944) - **couchbase-done:** fix datadog user job
- [`5abb1c8`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/5abb1c8747396ae000027d3b80b1210fc700e78e) - **couchbase-done:** modified secret reloader
- [`0e8daa2`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/0e8daa23763f7a563b51e64b149242bcd914fe5c) - **couchbase-done:** modified secrets-watcher to handle multiple secrets in subpaths
- [`d99b0ae`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d99b0ae11cd27328fc883e2c9d275e2b0203002e) - **couchbase-done:** modified secrets-watcher to handle multiple secrets in subpaths
- [`83a5cba`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/83a5cbab977cad7835e9513ea903ce35ad9e8eb2) - **couchbase-done:** modified secrets-watcher to handle multiple secrets in subpaths
- [`3692be9`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/3692be9c27ef2716287cc95093b6369a56303e69) - **couchbase-done:** modified secrets-watcher to handle multiple secrets in subpaths
- [`c96d744`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/c96d744d1d32555891a29614e22457b8e9993119) - **couchbase-done:** modified secrets-watcher to handle multiple secrets in subpaths
- [`92f04b7`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/92f04b70c54f5a7669e7ac41f1b67bd20196fe4b) - **couchbase-done:** modified secrets-watcher to handle multiple secrets in subpaths
- [`8729244`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/87292441b2d58f9a1e6d34a0c57f5a36db10dd7f) - **couchbase-done:** added secret-watcher pod to run secret creation jobs
- [`9aafece`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/9aafece13ed34fee0b2b4739928e888fc49696a7) - **couchbase-done:** change username password for rebalance config job
- [`e4de069`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/e4de069611a3147966fe4c404253e73471ca8b66) - **couchbase-done:** added datadog and couchbase-service-broker user
- [`703eaab`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/703eaab18a34b7fc4acf5f59be2d235fb31660be) - **couchbase-done:** testing rotating admin secret
- [`b4f9e99`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b4f9e99fbd1adce699ab42759c0fcfb3aee5db60) - **couchbase-done:** disabled managed rbac
- [`dad2a4e`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/dad2a4e1acf94145ab163642ff51de9772c77970) - **couchbase-done:** disabled managed rbac
- [`d0991b7`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/d0991b7a8604971d01e773d94f3efa1fafde5bed) - **couchbase-done:** testing rbac with labels
- [`f396a31`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/f396a31b1f84ab668fe102e95e5a4225faeea589) - **couchbase-done:** changed servicemonitor metrics port
- [`6678295`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/6678295d83951c638014c4797fa1a58f7a18d195) - **couchbase-done:** cleanup chart
- [`e62cf41`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/e62cf41d85454c66973aef3164c500e629443ad5) - **couchbase-done:** testing admin user creation
- [`9dd8f85`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/9dd8f85525fbf8c9636ca5a16b4094db64060300) - **couchbase-done:** testing admin user creation
- [`1e00846`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/1e00846b42925ecf51d35aae4d06e1c63dda3784) - **couchbase-done:** set rbac managed to true
- [`0523589`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/05235897a17f1a41c49baa9e91ee4b882b603af7) - **couchbase-done:** added datadog user in a declarative way
- [`c6dd4f2`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/c6dd4f2662b9a67a87a8a611529a1d40e78d95fe) - **couchbase-done:** removed pod name settings
- [`50396e1`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/50396e1fd6ee0db09ec0d1b8226f4664147b6748) - **couchbase-done:** testing pod name settings
- [`5599b98`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/5599b982afca77ebe3a6005485a5ab530f82a4b2) - **couchbase-done:** added node selector and tolerations for cb operator
- [`ee4596a`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/ee4596a5c1937784e899493c4c42756af6f14149) - **couchbase-done:** added job to set max-moves-per-node setting
- [`7ee8398`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/7ee8398ef8a35e54db00f5ec9f3b1359a63b1de1) - **couchbase-done:** changed nodeselector and tolerations for new node types
- [`4d6a9b2`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/4d6a9b2df424ab778d51239b1c4128cb49eba605) - **couchbase-done:** removed backup pvc autoscaling
- [`5850760`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/5850760e68514ea7e1ccf07371938b7dbe9968ed) - **couchbase-done:** fixed autoscaling naming
- [`90eaf1b`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/90eaf1b79cfba266032da12e82038bf6947fa990) - **couchbase-done:** fixed autoscaling naming
- [`6b21629`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/6b216298d9adce2a693dfbe232be46871b571c0a) - **couchbase-done:** changed tag pvc cronjob settings
- [`dbf1139`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/dbf113987320a01fa26fd85cd2f12b7ce97651c6) - **couchbase-done:** removed ttl from datadog user job
- [`0ecded8`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/0ecded87871241abcbbb83a8046deb810e695393) - **couchbase-done:** removed thp daemonset
- [`87ccea0`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/87ccea0bd8232c9e6b46bba6408a7164a3020342) - **couchbase-done:** istio changes
- [`9d46ef5`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/9d46ef54de28f125adf9839a4a4a153de4e0dd1d) - **couchbase-done:** istio changes
- [`8e91387`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/8e913877a01f69378c3498ec8498a56da861dd7d) - **couchbase-done:** removed search service
- [`6b3299c`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/6b3299c99c7d1a0c026e04ca26eab929e07c06b8) - **couchbase-done:** removed search service
- [`baad0b0`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/baad0b0ddd5bf6fed1cf402d290580444aaba0b5) - **couchbase-done:** removed search service
- [`bd36af2`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/bd36af2f9213e1523fbd0d52424c13a25e701f4c) - **couchbase-done:** some chart optimizations
- [`da959f7`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/da959f715d3d3e317301b1c062fd3d18b85aa1f0) - **couchbase-done:** changed istio settings to access ui
- [`8f212b8`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/8f212b8b2e44d3ad6d58b972106608d98059047b) - **couchbase-done:** changed daemonset tolerations
- [`980d62e`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/980d62e2ed949158fc16b98d24450bb32e52ba17) - **couchbase-done:** added cluster name
- [`183efab`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/183efab346a28c8258793190e8b85bdfc5c2a785) - **couchbase-done:** added cluster name
- [`caccc26`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/caccc269f62411dffd8c30d43e1c151dcdd2dce0) - **couchbase-done:** added cluster name
- [`b3d6d54`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/b3d6d54a9bb2e35b7840263180b445a8716096cf) - **couchbase-done:** removed default server
- [`69146bc`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/69146bc7a5a832038f1246eb4a58a7e36e02c651) - **couchbase-done:** fixed templating
- [`fd09d99`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fd09d9969456599a20fddc8a8af5dd440fdf651c) - **couchbase-done:** fixed prometheus tag
- [`3d4752f`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/3d4752ff4234b77ebc3218db9857699089be5b9c) - **couchbase-done:** added couchbase-done
- [`fa0e587`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/fa0e5871110bb79bcc1fa13c87e83a9e29d3d80e) - **couchbase-done:** added new values externalSecrets
- [`4f9abc8`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/4f9abc80e03e772a52aae2a4ec1e4a6a4fca263f) - **couchbase-done:** added new values domain and subdomain
- [`1ed2ea5`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/1ed2ea57ade59d953d7a2f0c7488f4983a2f42a2) - **couchbase-done:** added new value tolerations
- [`e8d3b76`](https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/commit/e8d3b76004c213a1c2665d305e800d8dbefc8ca8) - **couchbase-done:** initial commit


[Unreleased]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.22.0...HEAD
[couchbase-done/v1.22.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.21.0...couchbase-done/v1.22.0
[couchbase-done/v1.21.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.20.0...couchbase-done/v1.21.0
[couchbase-done/v1.20.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.19.0...couchbase-done/v1.20.0
[couchbase-done/v1.19.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.18.0...couchbase-done/v1.19.0
[couchbase-done/v1.18.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.17.0...couchbase-done/v1.18.0
[couchbase-done/v1.17.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.16.0...couchbase-done/v1.17.0
[couchbase-done/v1.16.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.15.0...couchbase-done/v1.16.0
[couchbase-done/v1.15.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.14.1...couchbase-done/v1.15.0
[couchbase-done/v1.14.1]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.14.0...couchbase-done/v1.14.1
[couchbase-done/v1.14.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.13.0...couchbase-done/v1.14.0
[couchbase-done/v1.13.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.12.0...couchbase-done/v1.13.0
[couchbase-done/v1.12.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.11.0...couchbase-done/v1.12.0
[couchbase-done/v1.11.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.10.0...couchbase-done/v1.11.0
[couchbase-done/v1.10.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.9.0...couchbase-done/v1.10.0
[couchbase-done/v1.9.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.8.0...couchbase-done/v1.9.0
[couchbase-done/v1.8.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.6.1...couchbase-done/v1.8.0
[couchbase-done/v1.6.1]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.7.0...couchbase-done/v1.6.1
[couchbase-done/v1.7.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.5.1...couchbase-done/v1.7.0
[couchbase-done/v1.5.1]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.6.0...couchbase-done/v1.5.1
[couchbase-done/v1.6.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.5.0...couchbase-done/v1.6.0
[couchbase-done/v1.5.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.4.3...couchbase-done/v1.5.0
[couchbase-done/v1.4.3]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.4.2...couchbase-done/v1.4.3
[couchbase-done/v1.4.2]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.4.1...couchbase-done/v1.4.2
[couchbase-done/v1.4.1]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.4.0...couchbase-done/v1.4.1
[couchbase-done/v1.4.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.3.2...couchbase-done/v1.4.0
[couchbase-done/v1.3.2]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.3.1...couchbase-done/v1.3.2
[couchbase-done/v1.3.1]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.3.0...couchbase-done/v1.3.1
[couchbase-done/v1.3.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.2.1...couchbase-done/v1.3.0
[couchbase-done/v1.2.1]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.2.0...couchbase-done/v1.2.1
[couchbase-done/v1.2.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.1.0...couchbase-done/v1.2.0
[couchbase-done/v1.1.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.0.4...couchbase-done/v1.1.0
[couchbase-done/v1.0.4]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.0.3...couchbase-done/v1.0.4
[couchbase-done/v1.0.3]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.0.2...couchbase-done/v1.0.3
[couchbase-done/v1.0.2]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.0.1...couchbase-done/v1.0.2
[couchbase-done/v1.0.1]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v1.0.0...couchbase-done/v1.0.1
[couchbase-done/v1.0.0]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.13...couchbase-done/v1.0.0
[couchbase-done/v0.0.13]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.12...couchbase-done/v0.0.13
[couchbase-done/v0.0.12]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.11...couchbase-done/v0.0.12
[couchbase-done/v0.0.11]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.10...couchbase-done/v0.0.11
[couchbase-done/v0.0.10]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.9...couchbase-done/v0.0.10
[couchbase-done/v0.0.9]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.8...couchbase-done/v0.0.9
[couchbase-done/v0.0.8]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.7...couchbase-done/v0.0.8
[couchbase-done/v0.0.7]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.6...couchbase-done/v0.0.7
[couchbase-done/v0.0.6]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.5...couchbase-done/v0.0.6
[couchbase-done/v0.0.5]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.4...couchbase-done/v0.0.5
[couchbase-done/v0.0.4]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.3...couchbase-done/v0.0.4
[couchbase-done/v0.0.3]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.2...couchbase-done/v0.0.3
[couchbase-done/v0.0.2]: https://github.vodafone.com/VFDE-SOL/k8s-modules-sol-done/compare/couchbase-done/v0.0.1...couchbase-done/v0.0.2
